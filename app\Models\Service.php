<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory, HasUuid;

    protected $guarded = [
        'status',
    ];

    function category(){
        return $this->belongsTo(Category::class);
    }
    
    function subcategory(){
        return $this->belongsTo(SubCategory::class);
    }

    function user(){
        return $this->belongsTo(User::class);
    }

    function availabilities(){
        return $this->hasMany(ServiceAvailability::class);
    }

    function scopeActive($query){
        return $query->where('status', 1);
    }

    public function staff()
    {
        return $this->belongsToMany(Staff::class, 'service_staff');
    }
    
}
