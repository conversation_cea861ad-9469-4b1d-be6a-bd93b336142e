<div>
    <form
        action="{{ isset($service) ? route('services.update', ['service' => $service->ids, 'type' => 'group']) : route('services.store', ['type' => 'group']) }}"
        method="POST" enctype="multipart/form-data" class="form-add-services" id="groupServiceForm">
        @csrf
        @isset($service)
            @method('PUT')
        @endisset
        <div class="row row-gap-5">
            {{-- Error Summary Container --}}
            <div id="error-summary" class="alert alert-danger" style="display: none;">
                <h5><i class="fas fa-exclamation-triangle"></i> Please fix the following errors:</h5>
                <ul id="error-list"></ul>
            </div>
            {{-- Service Name --}}
            <div class="col-md-12">
                <label for="service-name" class="form-label form-input-labels">
                    Service Name
                </label>
                <input type="text" class="form-control form-inputs-field" placeholder="Enter Service name"
                    id="service-name" name="name" value="{{ old('name', $service->name ?? '') }}">
                @error('name')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Name End --}}


            {{-- Category Name --}}
            <div class="col-md-6">
                <label for="category" class="form-label form-input-labels">Category</label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" id="category" name="category_id">
                    <option></option>
                    @forelse ($categories as $category)
                        <option value="{{ $category->ids }}"
                            {{ old('category_id', $service->category->ids ?? '') == $category->ids ? 'selected' : '' }}>
                            {{ $category->name }}</option>
                    @empty
                        <option value="">No categories found</option>
                    @endforelse
                </select>
                @error('category_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Category Name End --}}

            {{-- Subcategory --}}
            <div class="col-md-6">
                <label for="subcategory" class="form-label form-input-labels">Sub
                    Category</label>
                <select class="form-select form-select-field" data-control="select2"
                    data-dropdown-css-class="w-619px" data-close-on-select="false"
                    data-placeholder="Select an option" data-allow-clear="true" id="subcategory"
                    name="subcategory_id">
                    <option value="">Select Subcategory</option>
                    @if(isset($service) && $service->category)
                        @forelse ($service->category->subcategories as $subcategory)
                            <option value="{{ $subcategory->ids }}"
                                {{ old('subcategory_id', $service->subcategory->ids ?? '') == $subcategory->ids ? 'selected' : '' }}>
                                {{ $subcategory->name }}</option>
                        @empty
                        @endforelse
                    @endif
                </select>
                @error('subcategory_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Subcategory End --}}

            @if (auth()->check() && auth()->user()->hasRole('business'))
                <div class="col-md-12">
                    <label for="staff-member-secondary" class="form-label form-input-labels">Assign Staff
                        Members</label>
                    <select class="form-select form-select-field" data-control="select2"
                        data-dropdown-css-class="w-619px" data-close-on-select="false"
                        data-placeholder="Select staff members" data-allow-clear="true" multiple="multiple"
                        id="staff-member-secondary" name="staff_ids[]">
                        @forelse ($staffs as $staff)
                            <option value="{{ $staff->id }}"
                                {{ isset($service) && $service->staff->contains('id', $staff->id) ? 'selected' : '' }}>
                                {{ $staff->name }}</option>
                        @empty
                            <option disabled>No staff members available</option>
                        @endforelse
                    </select>
                    @error('staff_ids')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>
            @endif

            {{-- Availability --}}
            <div class="col-md-12">
                <x-service-availability-component :availabilities="$service->availabilities ?? []" />
            </div>
            {{-- Availability End --}}

            {{-- Service Duration --}}
            <div class="col-md-6">
                <label for="duration" class="form-label form-input-labels">Service Duration</label>
                <select name="duration" id="duration" class="form-control form-select-field">
                    <option value="">Select Service Duration</option>
                    <option value="15" {{ old('duration', $service->duration ?? '') == '15' ? 'selected' : '' }}>15
                        min
                    </option>
                    <option value="30" {{ old('duration', $service->duration ?? '') == '30' ? 'selected' : '' }}>30
                        min
                    </option>
                    <option value="45" {{ old('duration', $service->duration ?? '') == '45' ? 'selected' : '' }}>40
                        min
                    </option>
                    <option value="60" {{ old('duration', $service->duration ?? '') == '60' ? 'selected' : '' }}>60
                        min
                    </option>
                </select>
                @error('duration')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Duration End --}}



            {{-- <div class="col-md-6">
                <div class="d-flex  justify-content-between">
                    <label for="serviceTime" class="form-label form-input-labels">Service Day(s) &
                        Time</label>
                    <label class="styled-checkbox d-flex gap-3">
                        <input type="checkbox" name="recurring" id="recurring" checked>
                        <span class="fs-14 light-black normal">Recurring</span>
                    </label>
                </div>
                <div class="input-icon-wrapper">
                    <input type="text" id="serviceTime" class="form-control" readonly
                        placeholder="Select Service Day(s) & Time" />
                </div>
            </div> --}}
            {{-- <div class="col-md-6">
                <label for="recurring-duration" class="form-label form-input-labels">Recurring
                    Duration</label>
                <select class="form-select form-select-field" id="recurring-duration" name="recurring-duration"
                    data-control="select2" data-placeholder="Select">
                    <option></option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                </select>
            </div> --}}
            <div class="col-md-6">
                <label for="discount_recurring" class="form-label form-input-labels">Discount For Recurring
                    <span class="normal opacity-6 light-black">(For more than 1 slot)</span></label>
                <div class="input-group">
                    <span class="input-group-text" id="basic-addon1">%</span>
                    <input type="text" class="form-control form-inputs-field" placeholder="Enter discount %"
                        id="discount_recurring" name="discount_recurring" value="{{ old('discount_recurring', $service->discount_recurring ?? '') }}" />
                </div>
                @error('discount_recurring')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>

            {{-- Total Slots --}}
            <div class="col-md-6">
                <label for="total-slots" class="form-label form-input-labels">Total Slots</label>
                <input type="number" min="1" class="form-control form-inputs-field"
                    value="{{ old('total_slots', $service->total_slots ?? '') }}" placeholder="Enter total slots"
                    id="total-slots" name="total_slots">
                @error('total_slots')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Total Slots End --}}

            {{-- Price Per Slot --}}
            <div class="col-md-3">
                <label for="price-per-slot" class="form-label form-input-labels">Price Per Slot
                    <span class="normal opacity-6 light-black">(Inclusive VAT)</span></label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter price"
                    value="{{ old('price_per_slot', $service->price ?? '') }}" id="price-per-slot"
                    name="price_per_slot">
                @error('price_per_slot')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Price Per Slot End --}}

            {{-- Additional Costs --}}
            <div class="col-md-3">
                <label for="additional-costs" class="form-label form-input-labels">Additional
                    Costs</label>
                <input type="number" class="form-control form-inputs-field"
                    value="{{ old('additional_cost', $service->additional_cost ?? '') }}"
                    placeholder="Enter additional  costs" id="additional-costs" name="additional_cost">
                @error('additional-costs')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Additional Costs End --}}

            {{-- Required Items --}}
            <div class="col-md-12">
                <label for="required-items" class="form-label form-input-labels">Required
                    Items For Service</label>
                <input type="text" class="form-control form-inputs-field"
                    placeholder="Enter required items (separate with comma)" id="required-items"
                    name="required_items" value="{{ old('required_items', $service->required_items ?? '') }}">
                @error('required_items')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Required Items End --}}

            {{-- Description --}}
            <div class="col-md-12">
                <label for="description" class="form-label form-input-labels">Description</label>
                <textarea class="form-control form-inputs-field form-textarea-field" id="description" name="description"
                    rows="4" placeholder="Enter Description here">{{ old('description', $service->description ?? '') }}</textarea>
                @error('description')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Description End --}}

            {{-- Service Location --}}
            <div class="col-md-4 d-flex gap-4">
                <label class="d-flex gap-2 align-items-center">
                    <input class="form-check-input" type="radio" name="service_location" value="onsite"
                        id="onsite-radio" @checked(old('service_location', $service->is_onsite ?? false) == 'onsite' || old('service_location', $service->is_onsite ?? false) == true)>
                    <span>On-site</span>
                </label>
                <label class="d-flex gap-2 align-items-center">
                    <input class="form-check-input" type="radio" name="service_location"
                        value="outside-location" id="outside-location-radio" @checked(old('service_location', $service->outside_location ?? false) == 'outside-location' || (old('service_location') == null && ($service->outside_location ?? false) == true))>
                    <span>Outside Location</span>
                </label>
            </div>
            @error('service_location')
                <p class="text-danger">
                    {{ $message }}
                </p>
            @enderror
            {{-- Service Location End --}}

            {{-- Physical & Outside Location --}}
            <div class="row row-gap-5">
                <!-- Physical Location -->
                <div class="col-md-12 form-hide-box" id="physical-location-seconday-field">
                    <label for="physical-location-secondary" class="form-label form-input-labels">Physical
                        Location</label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field"
                        id="pac-input" name="physical_location"
                        placeholder="2715 Ash Dr. San Jose, South Dakota 83475"
                        value="{{ old('physical_location', $service->physical_location ?? '') }}">
                    <input type="hidden" name="lat" value="" id="latitude">
                    <input type="hidden" name="lng" value="" id="longitude">
                    <div class="custom_loc mt-2">
                        <div id="map" style="height: 300px"></div>
                    </div>
                </div>

                <!-- Outside Location -->
                <div class="col-md-12 form-hide-box" id="outside-location-field">
                    <label for="outside-location-secondary" class="form-label form-input-labels">Outside
                        Location</label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field"
                        id="outside-location-secondary" name="outside_location"
                        placeholder="Enter your outside location"
                        value="{{ old('outside_location', $service->outside_location ?? '') }}">
                </div>
                @error('outside_location')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
                @error('physical_location')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Physical & Outside Location End --}}


            <!-- Thumbnail Image -->
            <div class="col-md-4 ">
                <label for="thumbnail-secondary" class="form-label form-input-labels">Thumbnail
                    Image</label>
                <div class="position-relative ">
                    <div class="image-input {{ $service->image ?? null ? 'image-input-changed' : 'image-input-empty' }}"
                        data-kt-image-input="true">
                        <div class="image-input-wrapper"
                            style="background-image: url('{{ asset('website') . '/' . ($service->image ?? '') }}');">
                        </div>
                        <label
                            class="image-label flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                            data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Change avatar">
                            <i class="bi bi-upload upload-icon"></i>
                            <span>Upload Image</span>
                            <span>50x50 px</span>
                            <input type="file" name="thumbnail" accept=".png, .jpg, .jpeg" />
                        </label>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Cancel avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Remove avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                    </div>
                </div>
                @error('thumbnail')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            <div class="">
                <button type="submit" class="add-btn">
                    {{ $btn_text ?? 'Add' }}
                </button>
            </div>
        </div>
    </form>
</div>
@push('js')
    <script>
        $(document).ready(function() {
            // Category change handler for subcategories
            $('#category-secondary').on('change', function() {
                const categoryId = $(this).val();
                const subcategorySelect = $('#subcategory-secondary');

                // Clear subcategory options
                subcategorySelect.empty().append('<option value="">Select Subcategory</option>');

                if (categoryId) {
                    // Show loading state
                    subcategorySelect.append('<option disabled>Loading...</option>');

                    // Fetch subcategories
                    $.ajax({
                        url: `{{ route('services.get-subcategories', ':category_id') }}`.replace(':category_id', categoryId),
                        type: 'GET',
                        success: function(response) {
                            // Clear loading option
                            subcategorySelect.find('option[disabled]').remove();

                            if (response.success && response.subcategories.length > 0) {
                                $.each(response.subcategories, function(index, subcategory) {
                                    subcategorySelect.append(`<option value="${subcategory.ids}">${subcategory.name}</option>`);
                                });
                            } else {
                                subcategorySelect.append('<option disabled>No subcategories found</option>');
                            }

                            // Reinitialize Select2 if it's being used
                            if (subcategorySelect.hasClass('select2-hidden-accessible')) {
                                subcategorySelect.select2('destroy').select2();
                            }
                        },
                        error: function() {
                            subcategorySelect.find('option[disabled]').remove();
                            subcategorySelect.append('<option disabled>Error loading subcategories</option>');
                        }
                    });
                }
            });

            // Form validation and other existing code
            $('#groupServiceForm').validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 2,
                        maxlength: 225
                    },
                    category_id: {
                        required: true
                    },
                    subcategory_id: {
                        required: true
                    },
                    duration: {
                        required: true
                    },
                    price: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    thumbnail: {
                        required: function() {
                            // Only required if no existing image
                            return !$('.image-input').hasClass('image-input-changed');
                        }
                    },
                    additional_cost: {
                        number: true,
                        min: 0
                    },
                    required_items: {
                        maxlength: 1000
                    },
                    description: {},
                    service_location: {
                        required: true
                    },
                    physical_location: {
                        required: function() {
                            return $("#onsite-radio").is(':checked');
                        },
                        maxlength: 225
                    },
                    outside_location: {
                        required: function() {
                            return $("#outside-location-radio").is(':checked');
                        },
                        maxlength: 225
                    }
                },
                messages: {
                    name: {
                        required: "Name is required",
                        minlength: "Name must be at least 2 characters",
                        maxlength: "Name cannot exceed 225 characters"
                    },
                    category_id: {
                        required: "Please select a category"
                    },
                    subcategory_id: {
                        required: "Please select a subcategory"
                    },
                    duration: {
                        required: "Please select service duration"
                    },
                    price: {
                        required: "Price is required",
                        number: "Please enter a valid price",
                        min: "Price cannot be negative"
                    },
                    thumbnail: {
                        required: "Please upload a service image"
                    },
                    additional_cost: {
                        number: "Please enter a valid additional cost",
                        min: "Additional cost cannot be negative"
                    },
                    required_items: {
                        maxlength: "Required items description cannot exceed 1000 characters"
                    },
                    description: {},
                    service_location: {
                        required: "Please select a service location"
                    },
                    physical_location: {
                        required: "Physical location is required when On-site is selected",
                        maxlength: "Physical location cannot exceed 225 characters"
                    },
                    outside_location: {
                        required: "Outside location is required when Outside Location is selected",
                        maxlength: "Outside location cannot exceed 225 characters"
                    }
                },
                errorPlacement: function(error, element) {
                    // Custom error placement
                    if (element.attr("name") == "service_location") {
                        error.insertAfter("#service-location-field");
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });
        })
    </script>
@endpush
