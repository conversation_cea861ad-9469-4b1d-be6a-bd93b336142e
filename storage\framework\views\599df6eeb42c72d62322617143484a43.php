<style>
    .availability-modal .modal-dialog {
        max-width: 1100px;
    }

    .availability-modal .section-card {
        border-radius: 10px;
        border: 1px solid var(--input-border);
        background: var(--white);
        padding: 20px;
        margin-bottom: 20px;
    }

    .availability-modal .section-title {
        color: var(--black);
        font-family: Inter;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--neutral-gray);
    }

    .availability-modal .day-item {
        border-radius: 8px;
        border: 1px solid var(--neutral-gray);
        background: var(--white);
        padding: 15px;
        margin-bottom: 12px;
        transition: all 0.3s ease;
    }

    .availability-modal .day-item:hover {
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    }

    .availability-modal .styled-checkbox {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
    }

    .availability-modal .styled-checkbox input[type="checkbox"] {
        appearance: none;
        width: 18px;
        height: 18px;
        border: 2px solid var(--input-border);
        border-radius: 4px;
        background: var(--white);
        cursor: pointer;
        position: relative;
    }

    .availability-modal .styled-checkbox input[type="checkbox"]:checked {
        background: var(--deep-blue);
        border-color: var(--deep-blue);
    }

    .availability-modal .styled-checkbox input[type="checkbox"]:checked::after {
        content: "\f00c";
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
        color: var(--white);
        font-size: 10px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .availability-modal .time-input-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .availability-modal .time-input {
        border-radius: 8px;
        border: 1px solid var(--input-border);
        background: var(--white);
        padding: 8px 12px;
        width: 90px;
        font-size: 14px;
        text-align: center;
    }

    .availability-modal .time-input:focus {
        outline: none;
        border-color: var(--deep-blue);
        box-shadow: 0 0 0 2px rgba(2, 12, 135, 0.1);
    }

    .availability-modal .calendar-controls {
        background: var(--whisper-gray);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .availability-modal .btn-nav {
        border: none;
        background: var(--white);
        border-radius: 6px;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
    }

    .availability-modal .btn-nav:hover {
        background: var(--deep-blue);
        color: var(--white);
    }

    .availability-modal .recurring-options {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .availability-modal .radio-option {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 15px;
        border: 1px solid var(--input-border);
        border-radius: 20px;
        background: var(--white);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .availability-modal .radio-option:hover {
        border-color: var(--deep-blue);
    }

    .availability-modal .radio-option input[type="radio"] {
        appearance: none;
        width: 16px;
        height: 16px;
        border: 2px solid var(--input-border);
        border-radius: 50%;
        background: var(--white);
        cursor: pointer;
        position: relative;
    }

    .availability-modal .radio-option input[type="radio"]:checked {
        border-color: var(--deep-blue);
    }

    .availability-modal .radio-option input[type="radio"]:checked::after {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--deep-blue);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .availability-modal .radio-option:has(input:checked) {
        border-color: var(--deep-blue);
        background: rgba(2, 12, 135, 0.05);
    }



    .availability-modal .vacation-badge {
        background: var(--green);
        color: var(--white);
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 12px;
        margin: 2px;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .availability-modal .btn-primary-custom {
        background: var(--deep-blue);
        border: 1px solid var(--deep-blue);
        color: var(--white);
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .availability-modal .btn-primary-custom:hover {
        background: var(--ocean-blue);
        border-color: var(--ocean-blue);
    }

    .availability-modal .btn-outline-custom {
        background: transparent;
        border: 1px solid var(--input-border);
        color: var(--black);
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .availability-modal .btn-outline-custom:hover {
        border-color: var(--deep-blue);
        color: var(--deep-blue);
    }

    .availability-modal .max-height-300 {
        max-height: 300px;
    }

    .availability-modal .overflow-auto {
        overflow-y: auto;
    }

    .availability-modal .schedule-item {
        border: 1px solid var(--neutral-gray);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
        background: var(--white);
        transition: all 0.3s ease;
    }

    .availability-modal .schedule-item:hover {
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    }

    .availability-modal .day-item input:disabled {
        background: var(--whisper-gray);
        color: var(--gray);
        cursor: not-allowed;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .time-input-group {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .time-input-group {
        display: flex !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .closed-text {
        display: block !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .closed-text {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .add-time-slot-container {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .add-time-slot-container {
        display: block !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .additional-time-slots {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .additional-time-slots {
        display: block !important;
    }

    .availability-modal .add-time-slot-btn {
        background: var(--deep-blue);
        color: var(--white) !important;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
        margin: 8px auto 0;
        transition: all 0.3s ease;
    }

    .availability-modal .add-time-slot-btn i {
        color: var(--white) !important;
    }

    .availability-modal .add-time-slot-btn:hover {
        background: var(--ocean-blue);
        transform: scale(1.1);
    }

    .availability-modal .additional-time-slots {
        margin-top: 10px;
    }

    .availability-modal .additional-time-slot {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
        padding: 8px;
        background: var(--whisper-gray);
        border-radius: 6px;
    }

    .availability-modal .remove-time-slot-btn {
        background: var(--red);
        color: var(--white);
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 10px;
        transition: all 0.3s ease;
    }

    .availability-modal .remove-time-slot-btn:hover {
        background: #dc2626;
        transform: scale(1.1);
    }

    /* Time picker arrows styling */
    .availability-modal .time-input {
        position: relative;
    }

    .availability-modal .time-input-wrapper {
        position: relative;
        display: inline-block;
    }

    .availability-modal .time-arrows {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        flex-direction: column;
        gap: 2px;
        z-index: 10;
    }

    .availability-modal .time-arrow {
        width: 16px;
        height: 12px;
        background: var(--deep-blue);
        color: white;
        border: none;
        cursor: pointer;
        font-size: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 2px;
        transition: all 0.2s ease;
    }

    .availability-modal .time-arrow:hover {
        background: var(--ocean-blue);
        transform: scale(1.1);
    }

    .availability-modal .time-arrow.up {
        margin-bottom: 1px;
    }

    .availability-modal .time-arrow.down {
        margin-top: 1px;
    }

    /* Time input validation styling */
    .availability-modal .time-input.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .availability-modal .time-error {
        font-size: 11px;
        color: #dc3545;
        margin-top: 4px;
    }

    .availability-modal .time-input {
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .availability-modal .time-input:focus {
        border-color: var(--deep-blue);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        outline: 0;
    }

    .availability-modal .time-input {
        cursor: pointer;
    }

    .availability-modal .time-input:focus {
        border-color: var(--deep-blue);
        box-shadow: 0 0 0 2px rgba(2, 12, 135, 0.1);
    }

    .availability-modal .flatpickr-input {
        cursor: pointer !important;
    }

    /* Vacation Calendar Modal Specific Styles */
    .vacation-calendar-modal .modal-dialog {
        max-width: 1000px;
    }

    .vacation-calendar-modal .vacation-calendar-container {
        min-height: 400px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
    }

    .vacation-calendar-modal .vacation-preview-container {
        max-height: 300px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid var(--neutral-gray);
        border-radius: 8px;
        background: var(--whisper-gray);
    }

    .vacation-calendar-modal .vacation-preview-container .vacation-badge {
        display: block;
        margin-bottom: 8px;
        padding: 8px 12px;
        background: var(--green);
        color: var(--white);
        border-radius: 6px;
        font-size: 13px;
        position: relative;
    }

    .vacation-calendar-modal .vacation-preview-container .vacation-badge .btn-close {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 10px;
        opacity: 0.8;
    }

    .vacation-calendar-modal .vacation-preview-container .vacation-badge:hover .btn-close {
        opacity: 1;
    }

    /* Flatpickr Calendar Customization */
    .vacation-calendar-modal .flatpickr-calendar {
        width: 100% !important;
        max-width: 100% !important;
        font-size: 14px;
    }

    .vacation-calendar-modal .flatpickr-months {
        padding: 15px;
    }

    .vacation-calendar-modal .flatpickr-month {
        height: auto;
    }

    .vacation-calendar-modal .flatpickr-current-month {
        font-size: 18px;
        font-weight: 600;
        color: var(--deep-blue);
    }

    .vacation-calendar-modal .flatpickr-weekdays {
        background: var(--whisper-gray);
        padding: 10px 0;
    }

    .vacation-calendar-modal .flatpickr-weekday {
        font-weight: 600;
        color: var(--black);
        font-size: 13px;
    }

    .vacation-calendar-modal .flatpickr-days {
        padding: 10px;
    }

    .vacation-calendar-modal .flatpickr-day {
        width: 40px;
        height: 40px;
        line-height: 40px;
        margin: 2px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
    }

    .vacation-calendar-modal .flatpickr-day:hover {
        background: var(--ice-blue);
        border-color: var(--deep-blue);
    }

    .vacation-calendar-modal .flatpickr-day.selected {
        background: var(--deep-blue) !important;
        border-color: var(--deep-blue) !important;
        color: var(--white) !important;
    }

    .vacation-calendar-modal .flatpickr-day.selected:hover {
        background: var(--ocean-blue) !important;
    }



    /* Toastr positioning for modal */
    .toast-container {
        z-index: 999999 !important;
    }



    /* Responsive adjustments */
    @media (max-width: 991px) {
        .availability-modal .modal-dialog {
            max-width: 95%;
            margin: 1rem auto;
        }

        .availability-modal .section-card {
            padding: 15px;
            margin-bottom: 15px;
        }

        .availability-modal .day-item {
            padding: 12px;
        }

        .availability-modal .time-input-group {
            flex-direction: column;
            gap: 8px;
            align-items: stretch;
        }

        .availability-modal .time-input {
            width: 100%;
        }

        .availability-modal .recurring-options {
            flex-direction: column;
            gap: 10px;
        }

        .availability-modal .radio-option {
            justify-content: center;
        }
    }

    @media (max-width: 767px) {
        .availability-modal .modal-dialog {
            margin: 0.5rem;
        }

        .availability-modal .day-item .d-flex {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .availability-modal .calendar-controls {
            padding: 10px;
        }

        .availability-modal .calendar-controls h6 {
            font-size: 12px;
        }
    }
</style>

<div class="modal fade availability-modal" id="availabilityModal" tabindex="-1" aria-labelledby="availabilityModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-4">
                <h5 class="modal-title fs-18 semi-bold black" id="availabilityModalLabel">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>
                    Staff Availability Schedule
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <form>
                    <div class="row">
                        <!-- Left Column: Weekly Schedule -->
                        <div class="col-lg-7">
                            <div class="section-card">
                                <h6 class="section-title">
                                    <i class="fas fa-clock me-2"></i>
                                    Weekly Schedule
                                </h6>

                                <!-- Calendar Navigation -->
                                <div class="calendar-controls d-flex justify-content-between align-items-center">
                                    <button type="button" id="prev-week" class="btn-nav">
                                        <i class="fa-solid fa-chevron-left"></i>
                                    </button>
                                    <h6 id="date-range" class="m-0 fs-14 semi-bold black">Loading...</h6>
                                    <button type="button" id="next-week" class="btn-nav">
                                        <i class="fa-solid fa-chevron-right"></i>
                                    </button>
                                </div>

                                <!-- Days Schedule -->
                                <div class="days-schedule mt-3">
                                    <!-- Monday -->
                                    <div class="day-item" data-day="Monday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="monday-check">
                                                <input type="checkbox" class="day-toggle" id="monday-check">
                                                <span class="fs-14 semi-bold black">Monday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text" placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text" placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Monday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Tuesday -->
                                    <div class="day-item" data-day="Tuesday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="tuesday-check">
                                                <input type="checkbox" class="day-toggle" id="tuesday-check">
                                                <span class="fs-14 semi-bold black">Tuesday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text" placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text" placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Tuesday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Wednesday -->
                                    <div class="day-item" data-day="Wednesday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="wednesday-check">
                                                <input type="checkbox" class="day-toggle" id="wednesday-check">
                                                <span class="fs-14 semi-bold black">Wednesday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text" placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text" placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Wednesday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Thursday -->
                                    <div class="day-item" data-day="Thursday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="thursday-check">
                                                <input type="checkbox" class="day-toggle" id="thursday-check">
                                                <span class="fs-14 semi-bold black">Thursday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text" placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text" placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Thursday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Friday -->
                                    <div class="day-item" data-day="Friday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="friday-check">
                                                <input type="checkbox" class="day-toggle" id="friday-check">
                                                <span class="fs-14 semi-bold black">Friday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text" placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text" placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Friday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Saturday -->
                                    <div class="day-item" data-day="Saturday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="saturday-check">
                                                <input type="checkbox" class="day-toggle" id="saturday-check">
                                                <span class="fs-14 semi-bold black">Saturday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text" placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text" placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Saturday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Sunday -->
                                    <div class="day-item" data-day="Sunday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="sunday-check">
                                                <input type="checkbox" class="day-toggle" id="sunday-check">
                                                <span class="fs-14 semi-bold black">Sunday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text" placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text" placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Sunday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column: Settings & Options -->
                        <div class="col-lg-5">

                            <!-- Recurring Schedule Settings -->
                            <div class="section-card">
                                <h6 class="section-title">
                                    <i class="fas fa-repeat me-2"></i>
                                    Recurring Schedule
                                </h6>

                                <div class="recurring-options">
                                    <label class="radio-option">
                                        <input type="radio" name="duration" value="4">
                                        <span class="fs-13">4 Weeks</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="duration" value="8">
                                        <span class="fs-13">8 Weeks</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="duration" value="custom">
                                        <span class="fs-13">Custom</span>
                                    </label>
                                </div>

                                <div class="custom-weeks mt-3" id="custom-weeks" style="display: none;">
                                    <label class="form-label form-input-labels">Number of Weeks</label>
                                    <div class="d-flex gap-2">
                                        <input type="number" class="form-control form-inputs-field" id="custom-weeks-input"
                                            placeholder="Enter number of weeks" min="1" max="52">
                                        <button type="button" class="btn-primary-custom" id="apply-custom-weeks-btn">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>



                            <!-- Vacation Days Section -->
                            <div class="section-card">
                                <h6 class="section-title">
                                    <i class="fas fa-umbrella-beach me-2"></i>
                                    Vacation Days
                                </h6>

                                <p class="fs-12 text-muted mb-3">Select dates when you will be unavailable (these dates will be skipped in the schedule)</p>

                                <div class="mb-3">
                                    <button type="button" class="btn-outline-custom" id="vacation-calendar-btn">
                                        <i class="fas fa-calendar-alt me-1"></i> Select Vacation Dates
                                    </button>
                                </div>

                                <div id="selected-vacations" class="mb-3">
                                    <h6 class="fs-12 mb-2">Selected Vacation Dates:</h6>
                                    <div id="vacation-dates-list" class="d-flex flex-wrap gap-1">
                                        <span class="text-muted fs-12">No vacation dates selected</span>
                                    </div>
                                </div>

                                <input type="hidden" id="vacation-dates" name="vacation_dates">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0 p-4">
                <div class="d-flex justify-content-end gap-3">
                    <button type="button" class="cancel-btn" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="button" class="save-btn" id="save-availability-btn">
                        <i class="fas fa-check me-1"></i> Save Availability
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Vacation Calendar Modal -->
<div class="modal fade availability-modal vacation-calendar-modal" id="vacationCalendarModal" tabindex="-1" aria-labelledby="vacationCalendarModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-4">
                <h5 class="modal-title fs-16 semi-bold black" id="vacationCalendarModalLabel">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>
                    Select Vacation Dates
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="section-card h-100">
                            <h6 class="section-title">
                                <i class="fas fa-calendar me-2"></i>
                                Calendar
                            </h6>
                            <div id="vacation-calendar" class="vacation-calendar-container"></div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="section-card h-100">
                            <h6 class="section-title">
                                <i class="fas fa-list me-2"></i>
                                Selected Dates
                            </h6>
                            <div id="vacation-preview" class="vacation-preview-container">
                                <span class="text-muted fs-12">No dates selected</span>
                            </div>
                            <div class="mt-4">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <small>Click on dates in the calendar to select vacation days. Selected dates will be excluded from the staff schedule.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0 p-4">
                <div class="d-flex justify-content-end gap-3">
                    <button type="button" class="btn-outline-custom" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="button" class="btn-primary-custom" id="confirm-vacation-dates">
                        <i class="fas fa-check me-1"></i> Confirm Selection
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>



<?php $__env->startPush('js'); ?>
    <script>
        // Global time validation function
        function addTimeValidation(inputs) {
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    const dayItem = this.closest('.day-item');
                    const isStartTime = this.classList.contains('start-time') || this.classList.contains('additional-start-time');
                    const isEndTime = this.classList.contains('end-time') || this.classList.contains('additional-end-time');

                    let pairedInput;
                    if (isStartTime) {
                        pairedInput = this.parentNode.querySelector('.end-time, .additional-end-time');
                    } else if (isEndTime) {
                        pairedInput = this.parentNode.querySelector('.start-time, .additional-start-time');
                    }

                    // Validate this pair
                    if (pairedInput) {
                        validateTimeSlot(isStartTime ? this : pairedInput, isEndTime ? this : pairedInput);
                    }
                });
            });
        }

        // Global time validation for a pair of inputs
        function validateTimeSlot(startInput, endInput) {
            if (!startInput || !endInput) return;

            const startTime = startInput.value;
            const endTime = endInput.value;

            if (startTime && endTime) {
                const startMinutes = parseTime(startTime);
                const endMinutes = parseTime(endTime);

                if (startMinutes >= endMinutes) {
                    // End time should be after start time
                    const newEndTime = getNextTimeSlot(startTime, 60);
                    endInput.value = newEndTime;

                    // Show validation message
                    showValidationMessage(endInput, 'End time must be after start time');
                }
            }
        }

        // Helper function to show validation messages
        function showValidationMessage(input, message) {
            // Remove existing message
            const existingMessage = input.parentNode.querySelector('.validation-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // Add new message
            const messageDiv = document.createElement('div');
            messageDiv.className = 'validation-message text-danger fs-12 mt-1';
            messageDiv.textContent = message;
            input.parentNode.appendChild(messageDiv);

            // Remove message after 3 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 3000);
        }

        // Helper function to parse time string to minutes
        function parseTime(timeStr) {
            if (!timeStr) return null;
            const match = timeStr.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);
            if (!match) return null;

            let hours = parseInt(match[1]);
            const minutes = parseInt(match[2]);
            const period = match[3].toUpperCase();

            if (period === 'PM' && hours !== 12) hours += 12;
            if (period === 'AM' && hours === 12) hours = 0;

            return hours * 60 + minutes;
        }

        // Helper function to format minutes to time string
        function formatTime(minutes) {
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            const period = hours >= 12 ? 'PM' : 'AM';
            const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            return `${displayHours}:${mins.toString().padStart(2, '0')} ${period}`;
        }

        // Helper function to get next time slot
        function getNextTimeSlot(timeStr, addMinutes = 60) {
            const minutes = parseTime(timeStr);
            if (!minutes && minutes !== 0) return '8:00 PM';
            return formatTime(minutes + addMinutes);
        }

        // Global vacation display function
        function updateVacationDisplay() {
            // Get elements dynamically to ensure they exist
            const vacationDatesList = document.getElementById("vacation-dates-list");
            const vacationDatesInput = document.getElementById("vacation-dates");

            if (!vacationDatesList || !vacationDatesInput) {
                console.log('Vacation display elements not found, will update when available');
                return;
            }

            // Update the main vacation display
            if (selectedVacationDates.length === 0) {
                vacationDatesList.innerHTML = '<span class="text-muted fs-12">No vacation dates selected</span>';
                vacationDatesInput.value = '';
            } else {
                const dateElements = selectedVacationDates.map(dateStr => {
                    const date = new Date(dateStr);
                    const formattedDate = date.toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric'
                    });
                    return `
                        <span class="vacation-badge me-1 mb-1">
                            ${formattedDate}
                            <button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.7em;" onclick="removeVacationDate('${dateStr}')"></button>
                        </span>
                    `;
                });
                vacationDatesList.innerHTML = dateElements.join('');
                vacationDatesInput.value = selectedVacationDates.join(',');
            }
        }

        // Global vacation dates array (will be initialized properly later)
        let selectedVacationDates = [];

        // Global weekly selections storage
        let weeklySelections = {};

        // Global function to remove vacation date
        function removeVacationDate(dateStr) {
            selectedVacationDates = selectedVacationDates.filter(d => d !== dateStr);
            updateVacationDisplay();
        }

        // Make remove function accessible globally
        window.removeVacationDate = removeVacationDate;

        // Helper function to get week start date
        function getWeekStart(date) {
            const d = new Date(date);
            const day = d.getDay();
            const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
            return new Date(d.setDate(diff));
        }

        document.addEventListener("DOMContentLoaded", function () {
            let currentStartDate;

            const dateRangeElement = document.getElementById("date-range");
            const prevWeekButton = document.getElementById("prev-week");
            const nextWeekButton = document.getElementById("next-week");

            // Function to get current week's Monday
            function getCurrentWeekMonday() {
                const today = new Date();
                const dayOfWeek = today.getDay();
                const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
                const monday = new Date(today);
                monday.setDate(today.getDate() + daysToMonday);
                // Reset time to midnight for consistent comparison
                monday.setHours(0, 0, 0, 0);
                return monday;
            }

            // Function to normalize date to midnight for comparison
            function normalizeDate(date) {
                const normalized = new Date(date);
                normalized.setHours(0, 0, 0, 0);
                return normalized;
            }

            // Format the date to "dd MMMM, yyyy"
            function formatDate(date) {
                const options = { year: 'numeric', month: 'long', day: 'numeric' };
                return date.toLocaleDateString('en-GB', options);
            }

            function updateDateRange() {
                // Debug: Log the current date being used
                console.log('updateDateRange called with currentStartDate:', currentStartDate);

                const startDate = new Date(currentStartDate);
                const endDate = new Date(currentStartDate);
                endDate.setDate(startDate.getDate() + 6);

                const startFormatted = formatDate(startDate);
                const endFormatted = formatDate(endDate);
                dateRangeElement.innerText = `${startFormatted} - ${endFormatted}`;

                // Update week info for break functionality
                window.currentWeekStart = new Date(startDate);

                // Disable previous button if we're on current week or earlier
                const currentWeekMonday = getCurrentWeekMonday();
                console.log('Current week Monday:', currentWeekMonday);
                console.log('Displayed week Monday:', currentStartDate);

                const isCurrentWeekOrEarlier = normalizeDate(currentStartDate).getTime() <= currentWeekMonday.getTime();

                if (prevWeekButton) {
                    prevWeekButton.disabled = isCurrentWeekOrEarlier;
                    if (isCurrentWeekOrEarlier) {
                        prevWeekButton.style.opacity = '0.5';
                        prevWeekButton.style.cursor = 'not-allowed';
                    } else {
                        prevWeekButton.style.opacity = '1';
                        prevWeekButton.style.cursor = 'pointer';
                    }
                }

                // Disable past days if we're on current week
                if (isCurrentWeekOrEarlier) {
                    disablePastDays();
                } else {
                    enableAllDays();
                }

                // Load saved selections for this week
                loadWeekSelections();
            }

            function disablePastDays() {
                const today = new Date();
                const todayDayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

                document.querySelectorAll(".day-item").forEach(dayElement => {
                    const dayName = dayElement.getAttribute("data-day");
                    const dayIndex = dayNames.indexOf(dayName);
                    const checkbox = dayElement.querySelector(".day-toggle");
                    const timeInputGroup = dayElement.querySelector(".time-input-group");
                    const closedText = dayElement.querySelector(".closed-text");

                    // If this day has already passed (is before today)
                    if (dayIndex < todayDayOfWeek) {
                        // Disable and uncheck the checkbox
                        if (checkbox) {
                            checkbox.disabled = true;
                            checkbox.checked = false;
                        }

                        // Hide time inputs and show closed text
                        if (timeInputGroup) timeInputGroup.style.display = "none";
                        if (closedText) {
                            closedText.style.display = "block";
                            closedText.textContent = "Past Date";
                            closedText.style.color = "#999";
                        }

                        // Disable time inputs
                        const timeInputs = dayElement.querySelectorAll(".timePicker");
                        timeInputs.forEach(input => {
                            input.disabled = true;
                            input.setAttribute('readonly', 'readonly');
                        });

                        // Gray out the entire day item
                        dayElement.style.opacity = "0.5";
                        dayElement.style.pointerEvents = "none";
                    }
                });
            }

            function enableAllDays() {
                document.querySelectorAll(".day-item").forEach(dayElement => {
                    const checkbox = dayElement.querySelector(".day-toggle");
                    const closedText = dayElement.querySelector(".closed-text");

                    // Enable checkbox
                    if (checkbox) {
                        checkbox.disabled = false;
                    }

                    // Reset closed text
                    if (closedText) {
                        closedText.textContent = "Closed";
                        closedText.style.color = "";
                    }

                    // Reset day item styling
                    dayElement.style.opacity = "1";
                    dayElement.style.pointerEvents = "auto";
                });
            }



            // Initialize with current week and display it immediately
            currentStartDate = getCurrentWeekMonday();
            updateDateRange();



            // Add time slot functionality
            function addTimeSlot(dayName) {
                const dayItem = document.querySelector(`[data-day="${dayName}"]`);
                if (!dayItem) return;

                const additionalSlotsContainer = dayItem.querySelector('.additional-time-slots');
                if (!additionalSlotsContainer) return;

                // Get the last time slot's end time to set as default start time
                const lastEndTime = getLastEndTime(dayItem);
                const suggestedStartTime = getNextTimeSlot(lastEndTime);
                const suggestedEndTime = getNextTimeSlot(suggestedStartTime, 120); // 2 hours later

                const slotId = Date.now() + Math.random();
                const slotHtml = `
                    <div class="additional-time-slot" data-slot-id="${slotId}">
                        <input class="time-input additional-start-time" type="text" placeholder="e.g. ${suggestedStartTime}" value="${suggestedStartTime}" />
                        <span class="text-muted">to</span>
                        <input class="time-input additional-end-time" type="text" placeholder="e.g. ${suggestedEndTime}" value="${suggestedEndTime}" />
                        <button type="button" class="remove-time-slot-btn" onclick="removeTimeSlot('${dayName}', '${slotId}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                additionalSlotsContainer.insertAdjacentHTML('beforeend', slotHtml);

                // Add validation event listeners to new inputs
                const newSlot = additionalSlotsContainer.querySelector(`[data-slot-id="${slotId}"]`);
                addTimeValidation(newSlot.querySelectorAll('.time-input'));

                // Save selections after adding time slot
                setTimeout(saveCurrentWeekSelections, 100);
            }

            // Remove time slot functionality
            window.removeTimeSlot = function(dayName, slotId) {
                const dayItem = document.querySelector(`[data-day="${dayName}"]`);
                if (!dayItem) return;

                const slotToRemove = dayItem.querySelector(`[data-slot-id="${slotId}"]`);
                if (slotToRemove) {
                    slotToRemove.remove();
                    // Save selections after removing time slot
                    setTimeout(saveCurrentWeekSelections, 100);
                }
            };

            function getLastEndTime(dayItem) {
                // Get main end time
                const mainEndTime = dayItem.querySelector('.end-time').value || '7:00 PM';

                // Get all additional slots end times
                const additionalEndTimes = Array.from(dayItem.querySelectorAll('.additional-end-time'))
                    .map(input => input.value)
                    .filter(time => time);

                // Find the latest time
                let latestTime = mainEndTime;
                let latestMinutes = parseTime(mainEndTime);

                additionalEndTimes.forEach(time => {
                    const minutes = parseTime(time);
                    if (minutes && minutes > latestMinutes) {
                        latestTime = time;
                        latestMinutes = minutes;
                    }
                });

                return latestTime;
            }

            function getNextTimeSlot(timeStr, addMinutes = 60) {
                const minutes = parseTime(timeStr);
                if (!minutes && minutes !== 0) return '8:00 PM';
                return formatTime(minutes + addMinutes);
            }



            // Add event listeners for add time slot buttons
            document.addEventListener('click', function(e) {
                if (e.target.closest('.add-time-slot-btn')) {
                    const button = e.target.closest('.add-time-slot-btn');
                    const dayName = button.getAttribute('data-day');
                    addTimeSlot(dayName);
                }
            });







            function getWeekKey(date) {
                // Create a unique key for each week (YYYY-MM-DD of Monday)
                return date.toISOString().split('T')[0];
            }

            function saveCurrentWeekSelections() {
                const weekKey = getWeekKey(currentStartDate);
                const selections = {
                    days: {},
                    duration: null,
                    customWeeks: null
                };

                // Save day selections
                document.querySelectorAll(".day-toggle").forEach(checkbox => {
                    const parent = checkbox.closest(".day-item");
                    if (parent) {
                        const dayName = parent.getAttribute("data-day");
                        const startTimeInput = parent.querySelector(".start-time");
                        const endTimeInput = parent.querySelector(".end-time");

                        // Get additional time slots
                        const additionalSlots = [];
                        parent.querySelectorAll('.additional-time-slot').forEach(slot => {
                            const additionalStart = slot.querySelector('.additional-start-time');
                            const additionalEnd = slot.querySelector('.additional-end-time');
                            const slotId = slot.getAttribute('data-slot-id');

                            if (additionalStart && additionalEnd) {
                                additionalSlots.push({
                                    id: slotId,
                                    startTime: additionalStart.value,
                                    endTime: additionalEnd.value
                                });
                            }
                        });

                        selections.days[dayName] = {
                            checked: checkbox.checked,
                            startTime: startTimeInput ? startTimeInput.value : "10:00 AM",
                            endTime: endTimeInput ? endTimeInput.value : "7:00 PM",
                            additionalSlots: additionalSlots
                        };
                    }
                });

                const selectedDuration = document.querySelector("input[name='duration']:checked");
                if (selectedDuration) {
                    selections.duration = selectedDuration.value;
                }

                const customWeeksInput = document.getElementById("custom-weeks-input");
                if (customWeeksInput) {
                    selections.customWeeks = customWeeksInput.value;
                }

                weeklySelections[weekKey] = selections;
                console.log('Saved selections for week:', weekKey, selections);
            }

            function resetFormToCleanState() {
                // Reset all day checkboxes to unchecked
                document.querySelectorAll(".day-toggle").forEach(checkbox => {
                    if (!checkbox.disabled) { // Don't reset disabled (past) days
                        checkbox.checked = false;
                        const parent = checkbox.closest(".day-item");
                        if (parent) {
                            const timeInputGroup = parent.querySelector(".time-input-group");
                            const closedText = parent.querySelector(".closed-text");

                            if (timeInputGroup) timeInputGroup.style.display = "none";
                            if (closedText) closedText.style.display = "block";

                            const timeInputs = parent.querySelectorAll(".timePicker");
                            timeInputs.forEach(input => {
                                input.setAttribute('readonly', 'readonly');
                                input.disabled = true;
                            });

                            // Clear additional time slots
                            const additionalSlotsContainer = parent.querySelector('.additional-time-slots');
                            if (additionalSlotsContainer) {
                                additionalSlotsContainer.innerHTML = '';
                            }
                        }
                    }
                });

                // Reset recurring options

                document.querySelectorAll("input[name='duration']").forEach(radio => {
                    radio.checked = false;
                });

                const customWeeks = document.getElementById("custom-weeks");
                if (customWeeks) customWeeks.style.display = "none";
            }

            function loadWeekSelections() {
                const weekKey = getWeekKey(currentStartDate);
                const selections = weeklySelections[weekKey];

                console.log('Loading selections for week:', weekKey, selections);

                // First reset to clean state
                resetFormToCleanState();

                // If we have saved selections for this week, restore them
                if (selections && selections.days) {
                    console.log('Restoring selections...');

                    // Restore day selections
                    Object.keys(selections.days).forEach(dayName => {
                        const dayData = selections.days[dayName];
                        const dayElement = document.querySelector(`[data-day="${dayName}"]`);

                        if (dayElement && dayData.checked) {
                            console.log('Restoring', dayName, 'as checked');

                            const checkbox = dayElement.querySelector(".day-toggle");
                            const timeInputGroup = dayElement.querySelector(".time-input-group");
                            const closedText = dayElement.querySelector(".closed-text");
                            const startTimeInput = dayElement.querySelector(".start-time");
                            const endTimeInput = dayElement.querySelector(".end-time");

                            // Only restore if the day is not disabled (not a past day)
                            if (checkbox && !checkbox.disabled) {
                                checkbox.checked = true;

                                if (timeInputGroup) timeInputGroup.style.display = "flex";
                                if (closedText) closedText.style.display = "none";

                                if (startTimeInput) startTimeInput.value = dayData.startTime;
                                if (endTimeInput) endTimeInput.value = dayData.endTime;

                                const timeInputs = dayElement.querySelectorAll(".time-input");
                                addTimeValidation(timeInputs);

                                // Restore additional time slots
                                if (dayData.additionalSlots && dayData.additionalSlots.length > 0) {
                                    const additionalSlotsContainer = dayElement.querySelector('.additional-time-slots');
                                    if (additionalSlotsContainer) {
                                        // Clear existing additional slots
                                        additionalSlotsContainer.innerHTML = '';

                                        dayData.additionalSlots.forEach(slot => {
                                            const slotHtml = `
                                                <div class="additional-time-slot" data-slot-id="${slot.id}">
                                                    <input class="time-input additional-start-time" value="${slot.startTime}" type="text" placeholder="e.g. 8:00 PM" />
                                                    <span class="text-muted">to</span>
                                                    <input class="time-input additional-end-time" value="${slot.endTime}" type="text" placeholder="e.g. 10:00 PM" />
                                                    <button type="button" class="remove-time-slot-btn" onclick="removeTimeSlot('${dayName}', '${slot.id}')">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            `;
                                            additionalSlotsContainer.insertAdjacentHTML('beforeend', slotHtml);
                                        });

                                        // Add validation for restored additional slots
                                        const restoredInputs = additionalSlotsContainer.querySelectorAll('.time-input');
                                        addTimeValidation(restoredInputs);
                                    }
                                }
                            }
                        }
                    });

                    // Restore duration selections
                    if (selections.duration) {
                        const durationRadio = document.querySelector(`input[name='duration'][value='${selections.duration}']`);
                        if (durationRadio) {
                            durationRadio.checked = true;

                            if (selections.duration === 'custom') {
                                const customWeeks = document.getElementById("custom-weeks");
                                const customWeeksInput = document.getElementById("custom-weeks-input");
                                if (customWeeks) customWeeks.style.display = "block";
                                if (customWeeksInput && selections.customWeeks) {
                                    customWeeksInput.value = selections.customWeeks;
                                }
                            }
                        }
                    }
                }

                // Clear containers that should reset
                const schedulePreview = document.getElementById("schedule-preview");
                if (schedulePreview) schedulePreview.style.display = "none";
            }



            prevWeekButton.addEventListener("click", function (event) {
                event.preventDefault();

                console.log('Previous button clicked');
                console.log('Current currentStartDate before:', currentStartDate);

                // Don't allow going to previous weeks before current week
                const currentWeekMonday = getCurrentWeekMonday();
                const newDate = new Date(currentStartDate);
                newDate.setDate(newDate.getDate() - 7);

                console.log('Would go to:', newDate);
                console.log('Current week Monday:', currentWeekMonday);

                if (normalizeDate(newDate).getTime() < currentWeekMonday.getTime()) {
                    console.log('Blocked: trying to go before current week, setting to current week');
                    // If trying to go before current week, just set to current week
                    currentStartDate = getCurrentWeekMonday();
                    updateDateRange();
                    return;
                }

                console.log('Allowed: going to previous week');
                saveCurrentWeekSelections(); // Save current selections before changing week
                currentStartDate.setDate(currentStartDate.getDate() - 7);
                console.log('New currentStartDate:', currentStartDate);
                updateDateRange();
            });

            nextWeekButton.addEventListener("click", function (event) {
                event.preventDefault();
                console.log('Next button clicked, saving current selections');
                saveCurrentWeekSelections(); // Save current selections before changing week
                currentStartDate.setDate(currentStartDate.getDate() + 7);
                console.log('Moving to next week:', currentStartDate);
                updateDateRange();
            });


            // Checkbox toggle logic
            document.querySelectorAll(".day-toggle").forEach(checkbox => {
                const parent = checkbox.closest(".day-item");
                const day = parent.getAttribute("data-day");
                const timeInputGroup = parent.querySelector(".time-input-group");
                const closedText = parent.querySelector(".closed-text");

                checkbox.addEventListener("change", function () {
                    if (this.checked) {
                        // Show time inputs, hide closed text
                        if (timeInputGroup) {
                            timeInputGroup.style.display = "flex";
                        }
                        if (closedText) {
                            closedText.style.display = "none";
                        }

                        // Enable time inputs and initialize flatpickr
                        const timeInputs = parent.querySelectorAll(".timePicker");
                        timeInputs.forEach(input => {
                            input.removeAttribute('readonly');
                            input.disabled = false;

                            // Initialize flatpickr if not already done
                            if (!input._flatpickr) {
                                flatpickr(input, {
                                    enableTime: true,
                                    noCalendar: true,
                                    dateFormat: "h:i K",
                                    time_24hr: false,
                                    clickOpens: true,
                                    allowInput: false,
                                    minuteIncrement: 15,
                                    defaultHour: input.classList.contains('start-time') ? 10 : 19,
                                    defaultMinute: 0
                                });
                            }
                        });
                    } else {
                        // Hide time inputs, show closed text
                        if (timeInputGroup) {
                            timeInputGroup.style.display = "none";
                        }
                        if (closedText) {
                            closedText.style.display = "block";
                        }

                        // Disable time inputs
                        const timeInputs = parent.querySelectorAll(".timePicker");
                        timeInputs.forEach(input => {
                            input.setAttribute('readonly', 'readonly');
                            input.disabled = true;
                        });
                    }
                });
            });



            // Initialize the modal with current week immediately
            updateDateRange();

            // Ensure current week is shown when modal opens
            const availabilityModal = document.getElementById('availabilityModal');
            if (availabilityModal) {
                availabilityModal.addEventListener('show.bs.modal', function () {
                    // Always reset to current week when modal opens
                    currentStartDate = getCurrentWeekMonday();
                    updateDateRange();

                    // If in edit mode, populate the modal with existing data
                    if (isEditMode && editAvailabilityData.length > 0) {
                        populateModalWithEditData();
                    }

                    // Update vacation display when modal opens
                    setTimeout(() => {
                        updateVacationDisplay();
                    }, 200);
                });
            }

            // Add event listeners to save selections when form elements change
            document.addEventListener('change', function(e) {
                if (e.target.matches('.day-toggle')) {
                    console.log('Day toggle changed, saving selections');
                    setTimeout(saveCurrentWeekSelections, 100); // Small delay to ensure DOM is updated
                }

                if (e.target.matches('input[name="duration"]')) {
                    console.log('Duration changed:', e.target.value);
                    handleDurationChange(e.target.value);
                }

                if (e.target.matches('#custom-weeks-input')) {
                    console.log('Custom weeks input changed');
                    setTimeout(saveCurrentWeekSelections, 100);
                }
            });

            // Also save when time inputs change
            document.addEventListener('input', function(e) {
                if (e.target.matches('.timePicker')) {
                    console.log('Time input changed, saving selections');
                    setTimeout(saveCurrentWeekSelections, 100);
                }
            });

            // Add Apply button event listener for custom weeks
            const applyCustomWeeksBtn = document.getElementById("apply-custom-weeks-btn");
            if (applyCustomWeeksBtn) {
                applyCustomWeeksBtn.addEventListener('click', function() {
                    const customRadio = document.querySelector('input[name="duration"][value="custom"]');
                    const customWeeksInput = document.getElementById("custom-weeks-input");

                    if (customRadio && customRadio.checked && customWeeksInput && customWeeksInput.value) {
                        console.log('Apply button clicked, triggering duration change');
                        handleDurationChange('custom');
                    } else if (!customWeeksInput.value) {
                        toastr.warning('Please enter the number of weeks first.', 'Missing Input');
                    }
                });
            }

            // Handle duration selection and copy schedule to multiple weeks
            function handleDurationChange(selectedValue) {
                // Show/hide custom weeks input
                const customWeeks = document.getElementById("custom-weeks");
                if (selectedValue === 'custom') {
                    customWeeks.style.display = "block";
                } else {
                    customWeeks.style.display = "none";
                }

                // Get current week's selections
                const currentWeekSelections = getCurrentWeekSelections();

                // Check if user has selected any days
                const hasSelections = Object.values(currentWeekSelections.days).some(day => day.checked);

                if (!hasSelections) {
                    toastr.warning('Please select at least one working day first, then choose the duration.', 'No Schedule Selected');

                    // Uncheck the radio button
                    document.querySelector(`input[name="duration"][value="${selectedValue}"]`).checked = false;
                    return;
                }

                // Determine number of weeks to copy
                let weeksCount;
                if (selectedValue === 'custom') {
                    const customInput = document.getElementById("custom-weeks-input");
                    weeksCount = parseInt(customInput.value);
                    if (!weeksCount || weeksCount < 1) {
                        toastr.warning('Please enter a valid number of weeks.', 'Invalid Duration');
                        return;
                    }
                } else {
                    weeksCount = parseInt(selectedValue);
                }

                // Copy current week's schedule to the next weeks
                copyScheduleToMultipleWeeks(currentWeekSelections, weeksCount);

                // Show success message
                toastr.success(`Your schedule has been copied to the next ${weeksCount} weeks.`, 'Schedule Copied');
            }

            function getCurrentWeekSelections() {
                const selections = {
                    days: {},
                    duration: null,
                    customWeeks: null
                };

                // Get day selections
                document.querySelectorAll(".day-toggle").forEach(checkbox => {
                    const parent = checkbox.closest(".day-item");
                    if (parent) {
                        const dayName = parent.getAttribute("data-day");
                        const startTimeInput = parent.querySelector(".start-time");
                        const endTimeInput = parent.querySelector(".end-time");

                        selections.days[dayName] = {
                            checked: checkbox.checked,
                            startTime: startTimeInput ? startTimeInput.value : "10:00 AM",
                            endTime: endTimeInput ? endTimeInput.value : "7:00 PM"
                        };
                    }
                });

                // Get duration selection
                const selectedDuration = document.querySelector("input[name='duration']:checked");
                if (selectedDuration) {
                    selections.duration = selectedDuration.value;
                }

                const customWeeksInput = document.getElementById("custom-weeks-input");
                if (customWeeksInput) {
                    selections.customWeeks = customWeeksInput.value;
                }

                return selections;
            }

            function copyScheduleToMultipleWeeks(baseSelections, weeksCount) {
                const currentWeekMonday = getCurrentWeekMonday();

                // First, save current week selections to capture any unsaved additional slots
                saveCurrentWeekSelections();

                // Get the updated base selections with current form state
                const currentWeekKey = getWeekKey(currentStartDate);
                const updatedBaseSelections = weeklySelections[currentWeekKey] || baseSelections;

                // Copy to each week
                for (let weekOffset = 0; weekOffset < weeksCount; weekOffset++) {
                    const targetWeekMonday = new Date(currentWeekMonday);
                    targetWeekMonday.setDate(currentWeekMonday.getDate() + (weekOffset * 7));

                    const weekKey = getWeekKey(targetWeekMonday);

                    // Deep copy the selections to this week (including additionalSlots arrays)
                    const copiedDays = {};
                    Object.keys(updatedBaseSelections.days).forEach(dayName => {
                        const dayData = updatedBaseSelections.days[dayName];
                        copiedDays[dayName] = {
                            checked: dayData.checked,
                            startTime: dayData.startTime,
                            endTime: dayData.endTime,
                            additionalSlots: dayData.additionalSlots ? [...dayData.additionalSlots.map(slot => ({
                                id: Date.now() + Math.random(), // Generate new unique ID for each copy
                                startTime: slot.startTime,
                                endTime: slot.endTime
                            }))] : []
                        };
                    });

                    weeklySelections[weekKey] = {
                        days: copiedDays,
                        duration: updatedBaseSelections.duration,
                        customWeeks: updatedBaseSelections.customWeeks
                    };

                    console.log(`Copied schedule to week ${weekOffset + 1}:`, weekKey, weeklySelections[weekKey]);
                }

                // Reload the current week to show any changes
                loadWeekSelections();
            }


        });


        document.addEventListener("DOMContentLoaded", function () {
            // Initialize time validation for all existing time inputs
            function initializeTimeInputs() {
                const timeInputs = document.querySelectorAll('.time-input');
                addTimeValidation(timeInputs);
            }

            // Initialize on page load
            initializeTimeInputs();

            // Re-initialize when checkboxes are toggled
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('day-toggle')) {
                    setTimeout(initializeTimeInputs, 100);
                }
            });
        });


    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const customWeeksInput = document.getElementById("custom-weeks");

            document.querySelectorAll("input[name='duration']").forEach(function (radio) {
                radio.addEventListener("change", function () {
                    if (this.value === "custom") {
                        customWeeksInput.style.display = "block";
                    } else {
                        customWeeksInput.style.display = "none";
                    }
                });
            });
        });
    </script>

    <script>
        // Global variables (some already declared above)
        let scheduleData = [];
        let vacationCalendar = null;
        let currentWeekStart = new Date();
        let isEditMode = false;
        let editAvailabilityData = [];
        let editVacationsData = [];
        let editRecurringData = {};

        // Global validation functions
        function parseTime(timeStr) {
            if (!timeStr) return null;

            const timeRegex = /^(\d{1,2}):(\d{2})\s*(AM|PM)$/i;
            const match = timeStr.trim().match(timeRegex);

            if (!match) return null;

            let hours = parseInt(match[1]);
            const minutes = parseInt(match[2]);
            const period = match[3].toUpperCase();

            if (hours < 1 || hours > 12 || minutes < 0 || minutes > 59) return null;

            if (period === 'PM' && hours !== 12) hours += 12;
            if (period === 'AM' && hours === 12) hours = 0;

            return hours * 60 + minutes; // Return total minutes
        }

        function formatTime(minutes) {
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            const period = hours >= 12 ? 'PM' : 'AM';
            const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            return `${displayHours}:${mins.toString().padStart(2, '0')} ${period}`;
        }

        function showTimeError(input, message) {
            // Remove existing error message
            const existingError = input.parentNode.querySelector('.time-error');
            if (existingError) {
                existingError.remove();
            }

            // Add new error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'time-error text-danger fs-11 mt-1';
            errorDiv.textContent = message;
            input.parentNode.appendChild(errorDiv);

            // Remove error after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        }

        function validateTimeSlot(startInput, endInput) {
            const startTime = startInput.value.trim();
            const endTime = endInput.value.trim();

            // Clear previous errors
            startInput.classList.remove('is-invalid');
            endInput.classList.remove('is-invalid');

            if (!startTime || !endTime) return true; // Allow empty for now

            const startMinutes = parseTime(startTime);
            const endMinutes = parseTime(endTime);

            let hasError = false;

            // Validate format
            if (!startMinutes && startMinutes !== 0) {
                startInput.classList.add('is-invalid');
                showTimeError(startInput, 'Invalid time format. Use format like "10:00 AM"');
                hasError = true;
            }

            if (!endMinutes && endMinutes !== 0) {
                endInput.classList.add('is-invalid');
                showTimeError(endInput, 'Invalid time format. Use format like "7:00 PM"');
                hasError = true;
            }

            // Validate end time is after start time
            if (startMinutes !== null && endMinutes !== null && endMinutes <= startMinutes) {
                endInput.classList.add('is-invalid');
                showTimeError(endInput, 'End time must be after start time');
                hasError = true;
            }

            return !hasError;
        }

        function validateTimeSequence(dayItem) {
            const timeSlots = [];

            // Get main time slot
            const mainStart = dayItem.querySelector('.start-time');
            const mainEnd = dayItem.querySelector('.end-time');
            if (mainStart.value && mainEnd.value) {
                timeSlots.push({
                    start: parseTime(mainStart.value),
                    end: parseTime(mainEnd.value),
                    startInput: mainStart,
                    endInput: mainEnd,
                    type: 'main'
                });
            }

            // Get additional time slots
            dayItem.querySelectorAll('.additional-time-slot').forEach(slot => {
                const startInput = slot.querySelector('.additional-start-time');
                const endInput = slot.querySelector('.additional-end-time');
                if (startInput.value && endInput.value) {
                    timeSlots.push({
                        start: parseTime(startInput.value),
                        end: parseTime(endInput.value),
                        startInput: startInput,
                        endInput: endInput,
                        type: 'additional'
                    });
                }
            });

            // Sort by start time
            timeSlots.sort((a, b) => a.start - b.start);

            // Validate sequence
            let hasError = false;
            for (let i = 1; i < timeSlots.length; i++) {
                const prevSlot = timeSlots[i - 1];
                const currentSlot = timeSlots[i];

                if (currentSlot.start < prevSlot.end) {
                    currentSlot.startInput.classList.add('is-invalid');
                    showTimeError(currentSlot.startInput, 'Time slots cannot overlap. Start time must be after previous end time.');
                    hasError = true;
                }
            }

            return !hasError;
        }

        function validateAllTimeInputs() {
            let hasErrors = false;
            const errorMessages = [];

            // Check all checked days for time validation
            document.querySelectorAll('.day-item').forEach(dayItem => {
                const checkbox = dayItem.querySelector('.day-toggle');
                if (checkbox && checkbox.checked) {
                    const dayName = dayItem.getAttribute('data-day');

                    // Validate main time slot
                    const startInput = dayItem.querySelector('.start-time');
                    const endInput = dayItem.querySelector('.end-time');

                    if (!startInput.value.trim() || !endInput.value.trim()) {
                        errorMessages.push(`${dayName}: Please enter both start and end times.`);
                        hasErrors = true;
                    } else {
                        // Validate format and sequence
                        if (!validateTimeSlot(startInput, endInput)) {
                            hasErrors = true;
                        }
                    }

                    // Validate additional time slots
                    dayItem.querySelectorAll('.additional-time-slot').forEach(slot => {
                        const additionalStart = slot.querySelector('.additional-start-time');
                        const additionalEnd = slot.querySelector('.additional-end-time');

                        if (!additionalStart.value.trim() || !additionalEnd.value.trim()) {
                            errorMessages.push(`${dayName}: Please enter times for all additional slots or remove empty ones.`);
                            hasErrors = true;
                        } else {
                            if (!validateTimeSlot(additionalStart, additionalEnd)) {
                                hasErrors = true;
                            }
                        }
                    });

                    // Validate time sequence for this day
                    if (!validateTimeSequence(dayItem)) {
                        hasErrors = true;
                    }
                }
            });

            if (hasErrors) {
                if (errorMessages.length > 0) {
                    alert("Please fix the following errors:\n\n" + errorMessages.join('\n'));
                } else {
                    alert("Please fix the time validation errors before saving.");
                }
                return false;
            }

            return true;
        }

        // Global functions for edit mode
        window.setEditAvailabilityData = function(availabilityData, vacationsData, recurringData) {
            isEditMode = true;
            editAvailabilityData = availabilityData || [];
            editVacationsData = vacationsData || [];
            editRecurringData = recurringData || {};
            selectedVacationDates = vacationsData || [];
        };

        window.clearEditAvailabilityData = function() {
            isEditMode = false;
            editAvailabilityData = [];
            editVacationsData = [];
            editRecurringData = {};
            selectedVacationDates = [];
        };

        function populateModalWithEditData() {
            console.log('Populating modal with edit data:', editAvailabilityData);
            console.log('Recurring data:', editRecurringData);

            // If this is recurring data, organize it by weeks for navigation
            if (editRecurringData && editRecurringData.recurring) {
                console.log('Processing recurring data - organizing by weeks');

                // Group the availability data by weeks
                const weekData = {};
                editAvailabilityData.forEach(dayData => {
                    const date = new Date(dayData.date);
                    const weekStart = getWeekStart(date);
                    const weekKey = getWeekKey(weekStart);

                    if (!weekData[weekKey]) {
                        weekData[weekKey] = {
                            days: {},
                            duration: editRecurringData.duration,
                            customWeeks: editRecurringData.customWeeks
                        };
                    }

                    // Convert to the format expected by weeklySelections
                    const additionalSlots = dayData.additional_slots ? dayData.additional_slots.map((slot, index) => ({
                        id: Date.now() + index,
                        startTime: slot.start_time,
                        endTime: slot.end_time
                    })) : [];

                    weekData[weekKey].days[dayData.day] = {
                        checked: true,
                        startTime: dayData.main_slot ? dayData.main_slot.start_time : '',
                        endTime: dayData.main_slot ? dayData.main_slot.end_time : '',
                        additionalSlots: additionalSlots
                    };
                });

                // Store in weeklySelections
                Object.keys(weekData).forEach(weekKey => {
                    weeklySelections[weekKey] = weekData[weekKey];
                    console.log(`Stored week ${weekKey}:`, weeklySelections[weekKey]);
                });

                // Load the current week's data
                loadWeekSelections();
            } else {
                // Non-recurring data - populate directly
                console.log('Processing non-recurring data');
                populateDirectly();
            }
        }

        function populateDirectly() {
            // Clear existing selections first
            document.querySelectorAll('.day-item').forEach(dayItem => {
                const checkbox = dayItem.querySelector('.day-toggle');
                const dayName = dayItem.getAttribute('data-day');

                // Find if this day has availability data
                const dayData = editAvailabilityData.find(item => item.day === dayName);

                if (dayData) {
                    // Check the day
                    checkbox.checked = true;
                    dayItem.classList.add('selected');

                    // Set main time slot
                    if (dayData.main_slot) {
                        const startInput = dayItem.querySelector('.start-time');
                        const endInput = dayItem.querySelector('.end-time');
                        if (startInput) startInput.value = dayData.main_slot.start_time;
                        if (endInput) endInput.value = dayData.main_slot.end_time;
                    }

                    // Add additional time slots
                    if (dayData.additional_slots && dayData.additional_slots.length > 0) {
                        const additionalSlotsContainer = dayItem.querySelector('.additional-time-slots');
                        if (additionalSlotsContainer) {
                            additionalSlotsContainer.innerHTML = ''; // Clear existing

                            dayData.additional_slots.forEach((slot, index) => {
                                const slotId = Date.now() + index;
                                const slotHtml = `
                                    <div class="additional-time-slot" data-slot-id="${slotId}">
                                        <input class="time-input additional-start-time" type="text" value="${slot.start_time}" />
                                        <span class="text-muted">to</span>
                                        <input class="time-input additional-end-time" type="text" value="${slot.end_time}" />
                                        <button type="button" class="remove-time-slot-btn" onclick="removeTimeSlot('${dayName}', '${slotId}')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                `;
                                additionalSlotsContainer.insertAdjacentHTML('beforeend', slotHtml);
                            });
                        }
                    }

                    // Show time inputs and hide closed text
                    const timeInputsContainer = dayItem.querySelector('.time-inputs');
                    const closedText = dayItem.querySelector('.closed-text');
                    const addTimeSlotContainer = dayItem.querySelector('.add-time-slot-container');
                    const additionalSlotsContainer = dayItem.querySelector('.additional-time-slots');

                    if (timeInputsContainer) timeInputsContainer.style.display = 'flex';
                    if (closedText) closedText.style.display = 'none';
                    if (addTimeSlotContainer) addTimeSlotContainer.style.display = 'block';
                    if (additionalSlotsContainer) additionalSlotsContainer.style.display = 'block';
                } else {
                    // Uncheck the day
                    checkbox.checked = false;
                    dayItem.classList.remove('selected');
                }
            });

            // Update vacation display
            if (editVacationsData.length > 0) {
                selectedVacationDates = [...editVacationsData];
                // Use setTimeout to ensure DOM elements are available
                setTimeout(() => {
                    updateVacationDisplay();
                }, 100);
            }

            // Set recurring options if available
            if (editRecurringData && editRecurringData.recurring && editRecurringData.duration) {
                // Set duration radio button
                const durationRadio = document.querySelector(`input[name="duration"][value="${editRecurringData.duration}"]`);
                if (durationRadio) {
                    durationRadio.checked = true;

                    // If custom, set the custom weeks value and show the input
                    if (editRecurringData.duration === 'custom' && editRecurringData.customWeeks) {
                        const customWeeksInput = document.getElementById('custom-weeks-input');
                        const customWeeksContainer = document.getElementById('custom-weeks');

                        if (customWeeksInput) {
                            customWeeksInput.value = editRecurringData.customWeeks;
                        }

                        if (customWeeksContainer) {
                            customWeeksContainer.style.display = 'block';
                        }
                    }
                }
            }
        }

        document.addEventListener("DOMContentLoaded", function () {





            function displayAvailableDates(selectedDays) {
                const weekStart = window.currentWeekStart || new Date();
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                let html = '';

                selectedDays.forEach(dayName => {
                    const dayIndex = dayNames.indexOf(dayName);
                    const date = new Date(weekStart);
                    date.setDate(weekStart.getDate() + dayIndex);

                    const dateStr = date.toISOString().split('T')[0];
                    const formattedDate = date.toLocaleDateString('en-US', {
                        weekday: 'long',
                        month: 'short',
                        day: 'numeric'
                    });

                    html += `
                        <div class="date-card" data-date="${dateStr}" data-day="${dayName}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${formattedDate}</strong>
                                    <div class="fs-12 text-muted">Click to add breaks</div>
                                </div>
                                <div class="break-count">
                                    <span class="badge bg-secondary" id="break-count-${dateStr}">0 breaks</span>
                                </div>
                            </div>
                            <div class="break-time-inputs" id="breaks-${dateStr}">
                                <button type="button" class="add-break-btn" onclick="addBreakTime('${dateStr}')">
                                    <i class="fas fa-plus me-1"></i> Add Break
                                </button>
                            </div>
                        </div>
                    `;
                });

                availableDatesList.innerHTML = html;

                // Add click handlers for date cards
                document.querySelectorAll('.date-card').forEach(card => {
                    card.addEventListener('click', function() {
                        const breakInputs = this.querySelector('.break-time-inputs');
                        if (breakInputs.classList.contains('show')) {
                            breakInputs.classList.remove('show');
                            this.classList.remove('selected');
                        } else {
                            // Hide all other break inputs
                            document.querySelectorAll('.break-time-inputs').forEach(input => {
                                input.classList.remove('show');
                            });
                            document.querySelectorAll('.date-card').forEach(c => {
                                c.classList.remove('selected');
                            });

                            // Show this one
                            breakInputs.classList.add('show');
                            this.classList.add('selected');
                        }
                    });
                });
            }

            // Generate schedule functionality (for recurring schedules)
            const generateScheduleBtn = document.getElementById("generate-schedule-btn");
            const schedulePreview = document.getElementById("schedule-preview");
            const scheduleDatesContainer = document.getElementById("schedule-dates-container");

            if (generateScheduleBtn) {
                generateScheduleBtn.addEventListener("click", function() {
                    // Save current week selections first
                    saveCurrentWeekSelections();

                    const recurringData = collectRecurringData();
                    const weeksToGenerate = calculateWeeks(recurringData);

                    // For recurring schedules, collect data from all saved weeks
                    const allWeeksData = collectAllWeeksData(weeksToGenerate);
                    if (allWeeksData.length === 0) {
                        toastr.warning('Please select at least one working day first.', 'No Working Days Selected');
                        return;
                    }

                    scheduleData = generateRecurringSchedule(allWeeksData, weeksToGenerate);
                    displaySchedulePreview(scheduleData);
                    schedulePreview.style.display = 'block';
                });
            }



            function collectBasicAvailabilityData() {
                const data = { days: [] };
                document.querySelectorAll(".day-item").forEach(dayElement => {
                    const checkbox = dayElement.querySelector(".day-toggle");
                    if (checkbox && checkbox.checked) {
                        const dayName = dayElement.getAttribute("data-day");
                        const startTimeInput = dayElement.querySelector(".start-time");
                        const endTimeInput = dayElement.querySelector(".end-time");

                        const startTime = startTimeInput ? startTimeInput.value : "10:00 AM";
                        const endTime = endTimeInput ? endTimeInput.value : "7:00 PM";

                        // Collect additional time slots
                        const additionalSlots = [];
                        dayElement.querySelectorAll('.additional-time-slot').forEach(slot => {
                            const additionalStart = slot.querySelector('.additional-start-time');
                            const additionalEnd = slot.querySelector('.additional-end-time');

                            if (additionalStart && additionalEnd) {
                                additionalSlots.push({
                                    start_time: additionalStart.value,
                                    end_time: additionalEnd.value
                                });
                            }
                        });

                        data.days.push({
                            day: dayName,
                            start_time: startTime,
                            end_time: endTime,
                            additional_slots: additionalSlots
                        });
                    }
                });
                return data;
            }

            function collectRecurringData() {
                const selectedDuration = document.querySelector("input[name='duration']:checked");
                return {
                    recurring: !!selectedDuration, // true if any duration is selected
                    duration: selectedDuration?.value,
                    customWeeks: document.getElementById("custom-weeks-input")?.value
                };
            }

            function calculateWeeks(recurringData) {
                if (!recurringData.recurring) return 1;

                switch (recurringData.duration) {
                    case '4weeks': return 4;
                    case '8weeks': return 8;
                    case 'custom': return parseInt(recurringData.customWeeks) || 1;
                    default: return 1;
                }
            }

            // Collect data from all saved weeks for recurring schedule generation
            function collectAllWeeksData(weeksToGenerate) {
                const allWeeksData = [];
                const baseWeekStart = new Date(currentStartDate);

                for (let weekOffset = 0; weekOffset < weeksToGenerate; weekOffset++) {
                    const weekStart = new Date(baseWeekStart);
                    weekStart.setDate(baseWeekStart.getDate() + (weekOffset * 7));
                    const weekKey = getWeekKey(weekStart);

                    // Get saved selections for this week
                    const weekSelections = weeklySelections[weekKey];

                    if (weekSelections && weekSelections.days) {
                        Object.keys(weekSelections.days).forEach(dayName => {
                            const dayData = weekSelections.days[dayName];
                            if (dayData.checked) {
                                const weekData = {
                                    week: weekOffset + 1,
                                    weekStart: weekStart,
                                    day: dayName,
                                    start_time: dayData.startTime,
                                    end_time: dayData.endTime,
                                    additional_slots: dayData.additionalSlots || []
                                };
                                allWeeksData.push(weekData);
                            }
                        });
                    }
                }

                return allWeeksData;
            }

            // Generate recurring schedule from all weeks data
            function generateRecurringSchedule(allWeeksData, weeksToGenerate) {
                const schedule = [];
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

                allWeeksData.forEach(weekData => {
                    const dayIndex = dayNames.indexOf(weekData.day);
                    const currentDate = new Date(weekData.weekStart);
                    currentDate.setDate(weekData.weekStart.getDate() + dayIndex);

                    // Skip vacation dates
                    const dateStr = currentDate.toISOString().split('T')[0];
                    if (!selectedVacationDates.includes(dateStr)) {
                        // Add main time slot
                        schedule.push({
                            date: dateStr,
                            day: weekData.day,
                            start_time: weekData.start_time,
                            end_time: weekData.end_time,
                            week: weekData.week,
                            breaks: [],
                            slot_type: 'main'
                        });

                        // Add additional time slots if they exist
                        if (weekData.additional_slots && weekData.additional_slots.length > 0) {
                            weekData.additional_slots.forEach((slot, index) => {
                                schedule.push({
                                    date: dateStr,
                                    day: weekData.day,
                                    start_time: slot.startTime,
                                    end_time: slot.endTime,
                                    week: weekData.week,
                                    breaks: [],
                                    slot_type: 'additional',
                                    slot_index: index + 1
                                });
                            });
                        }
                    }
                });

                return schedule;
            }

            function generateScheduleDates(days, weeks) {
                const schedule = [];
                const startDate = window.currentWeekStart || new Date();

                // Ensure we start from Monday of the current displayed week
                const baseDate = new Date(startDate);
                const dayOfWeek = baseDate.getDay();
                const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
                baseDate.setDate(baseDate.getDate() + daysToMonday);

                for (let week = 0; week < weeks; week++) {
                    days.forEach(dayData => {
                        const dayOfWeek = getDayOfWeekNumber(dayData.day);
                        const currentDate = new Date(baseDate);
                        currentDate.setDate(baseDate.getDate() + (week * 7) + dayOfWeek);

                        // Skip vacation dates
                        const dateStr = currentDate.toISOString().split('T')[0];
                        if (!selectedVacationDates.includes(dateStr)) {
                            // Add main time slot
                            schedule.push({
                                date: dateStr,
                                day: dayData.day,
                                start_time: dayData.start_time,
                                end_time: dayData.end_time,
                                week: week + 1,
                                breaks: [],
                                slot_type: 'main'
                            });

                            // Add additional time slots if they exist
                            if (dayData.additional_slots && dayData.additional_slots.length > 0) {
                                dayData.additional_slots.forEach((slot, index) => {
                                    schedule.push({
                                        date: dateStr,
                                        day: dayData.day,
                                        start_time: slot.start_time,
                                        end_time: slot.end_time,
                                        week: week + 1,
                                        breaks: [],
                                        slot_type: 'additional',
                                        slot_index: index + 1
                                    });
                                });
                            }
                        }
                    });
                }
                return schedule;
            }

            function getDayOfWeekNumber(dayName) {
                const days = {
                    'Monday': 0, 'Tuesday': 1, 'Wednesday': 2, 'Thursday': 3,
                    'Friday': 4, 'Saturday': 5, 'Sunday': 6
                };
                return days[dayName] || 0;
            }

            function displaySchedulePreview(schedule) {
                let html = '';

                // Group by week, then by date
                const weekGroups = {};
                schedule.forEach(item => {
                    if (!weekGroups[item.week]) {
                        weekGroups[item.week] = {};
                    }
                    if (!weekGroups[item.week][item.date]) {
                        weekGroups[item.week][item.date] = [];
                    }
                    weekGroups[item.week][item.date].push(item);
                });

                Object.keys(weekGroups).forEach(weekNum => {
                    html += `<div class="week-group mb-4">`;
                    html += `<h6 class="fs-13 semi-bold text-primary mb-3">Week ${weekNum}</h6>`;

                    Object.keys(weekGroups[weekNum]).forEach(date => {
                        const dateItems = weekGroups[weekNum][date];
                        const dateObj = new Date(date);
                        const formattedDate = dateObj.toLocaleDateString('en-US', {
                            weekday: 'long',
                            month: 'short',
                            day: 'numeric'
                        });

                        html += `
                            <div class="schedule-item mb-2 p-3 border rounded" data-date="${date}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>${formattedDate}</strong>
                        `;

                        // Display all time slots for this date
                        dateItems.forEach((item, index) => {
                            const slotLabel = item.slot_type === 'additional' ? ` (Slot ${item.slot_index + 1})` : '';
                            html += `<div class="fs-12 text-muted">${item.start_time} - ${item.end_time}${slotLabel}</div>`;
                        });

                        html += `
                                    </div>
                                    <div class="text-end">
                                        <div class="badge bg-success fs-10">Working Day</div>
                                        ${dateItems.length > 1 ? `<div class="badge bg-info fs-10 mt-1">${dateItems.length} Time Slots</div>` : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    html += `</div>`;
                });

                if (schedule.length === 0) {
                    html = '<div class="text-center text-muted py-4">No schedule generated. Please select working days and recurring options.</div>';
                }

                scheduleDatesContainer.innerHTML = html;
            }





            window.getScheduleData = function() {
                return scheduleData;
            };
        });

        // Vacation calendar functionality
        document.addEventListener("DOMContentLoaded", function () {
            const vacationCalendarBtn = document.getElementById("vacation-calendar-btn");
            const vacationCalendarModal = new bootstrap.Modal(document.getElementById('vacationCalendarModal'));
            const confirmVacationBtn = document.getElementById("confirm-vacation-dates");
            const vacationDatesList = document.getElementById("vacation-dates-list");
            const vacationDatesInput = document.getElementById("vacation-dates");

            // Open vacation calendar modal
            vacationCalendarBtn.addEventListener('click', function() {
                vacationCalendarModal.show();
                initializeVacationCalendar();
            });

            function initializeVacationCalendar() {
                // Destroy existing calendar if it exists
                if (vacationCalendar) {
                    vacationCalendar.destroy();
                }

                // Initialize flatpickr calendar
                vacationCalendar = flatpickr("#vacation-calendar", {
                    mode: "multiple",
                    inline: true,
                    dateFormat: "Y-m-d",
                    minDate: "today",
                    defaultDate: selectedVacationDates,
                    onChange: function(selectedDates, dateStr, instance) {
                        updateVacationPreview(selectedDates);
                    }
                });
            }

            function updateVacationPreview(selectedDates) {
                const vacationPreview = document.getElementById("vacation-preview");

                if (selectedDates.length === 0) {
                    vacationPreview.innerHTML = '<span class="text-muted fs-12">No dates selected</span>';
                } else {
                    const dateElements = selectedDates.map((date, index) => {
                        const formattedDate = date.toLocaleDateString('en-US', {
                            weekday: 'short',
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                        });
                        return `
                            <div class="vacation-badge">
                                ${formattedDate}
                                <button type="button" class="btn-close btn-close-white" onclick="removeVacationDateFromPreview(${index})"></button>
                            </div>
                        `;
                    });
                    vacationPreview.innerHTML = dateElements.join('');
                }
            }

            // Function to remove vacation date from preview
            window.removeVacationDateFromPreview = function(index) {
                if (vacationCalendar && vacationCalendar.selectedDates[index]) {
                    const dateToRemove = vacationCalendar.selectedDates[index];
                    const newSelectedDates = vacationCalendar.selectedDates.filter((date, i) => i !== index);
                    vacationCalendar.setDate(newSelectedDates);
                    updateVacationPreview(newSelectedDates);
                }
            };

            // Confirm vacation dates selection
            confirmVacationBtn.addEventListener('click', function() {
                if (vacationCalendar) {
                    selectedVacationDates = vacationCalendar.selectedDates.map(date =>
                        date.toISOString().split('T')[0]
                    );

                    updateVacationDisplay();
                    vacationCalendarModal.hide();
                }
            });





            // Make vacation dates accessible globally
            window.getSelectedVacationDates = function() {
                return selectedVacationDates;
            };
        });

        // Save availability functionality
        document.addEventListener("DOMContentLoaded", function () {
            const saveBtn = document.getElementById("save-availability-btn");

            saveBtn.addEventListener("click", function() {
                // First validate all time inputs
                if (!validateAllTimeInputs()) {
                    return; // Don't proceed if validation fails
                }

                const availabilityData = collectAvailabilityData();

                if (validateAvailabilityData(availabilityData)) {
                    // Determine if we're in edit mode or create mode
                    const isEditMode = document.getElementById('edit-availability-data') !== null;

                    if (isEditMode) {
                        // Store data in edit modal hidden inputs
                        document.getElementById('edit-availability-data').value = JSON.stringify(availabilityData.days);
                        document.getElementById('edit-vacations-data').value = JSON.stringify(availabilityData.vacations);
                        document.getElementById('edit-recurring-data').value = JSON.stringify({
                            recurring: availabilityData.recurring,
                            duration: availabilityData.duration,
                            customWeeks: availabilityData.customWeeks
                        });

                        // Update the edit availability button
                        const editAvailabilityBtn = document.getElementById('edit-availability-btn');
                        if (editAvailabilityBtn) {
                            editAvailabilityBtn.innerHTML = 'Availability Set <span><i class="fa-solid fa-check text-success"></i></span>';
                            editAvailabilityBtn.classList.add('text-success');
                        }
                    } else {
                        // Store data in create modal hidden inputs
                        document.getElementById('availability-data').value = JSON.stringify(availabilityData.days);
                        document.getElementById('vacations-data').value = JSON.stringify(availabilityData.vacations);
                        document.getElementById('recurring-data').value = JSON.stringify({
                            recurring: availabilityData.recurring,
                            duration: availabilityData.duration,
                            customWeeks: availabilityData.customWeeks
                        });

                        // Update the create availability button
                        const availabilityBtn = document.getElementById('availability-btn');
                        if (availabilityBtn) {
                            availabilityBtn.innerHTML = 'Availability Set <span><i class="fa-solid fa-check text-success"></i></span>';
                            availabilityBtn.classList.add('text-success');
                        }
                    }

                    // Close the modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('availabilityModal'));
                    modal.hide();
                }
            });

            function collectAvailabilityData() {
                const data = {
                    days: [],
                    vacations: [],
                    recurring: false,
                    duration: null,
                    customWeeks: null
                };

                // Collect selected days and times
                document.querySelectorAll(".day-item").forEach(dayElement => {
                    const checkbox = dayElement.querySelector(".day-toggle");
                    if (checkbox && checkbox.checked) {
                        const dayName = dayElement.getAttribute("data-day");
                        const startTimeInput = dayElement.querySelector(".start-time");
                        const endTimeInput = dayElement.querySelector(".end-time");

                        const startTime = startTimeInput ? startTimeInput.value : "10:00 AM";
                        const endTime = endTimeInput ? endTimeInput.value : "7:00 PM";

                        // Collect additional time slots
                        const additionalSlots = [];
                        dayElement.querySelectorAll('.additional-time-slot').forEach(slot => {
                            const additionalStart = slot.querySelector('.additional-start-time');
                            const additionalEnd = slot.querySelector('.additional-end-time');

                            if (additionalStart && additionalEnd) {
                                additionalSlots.push({
                                    start_time: additionalStart.value,
                                    end_time: additionalEnd.value
                                });
                            }
                        });



                        data.days.push({
                            day: dayName,
                            start_time: startTime,
                            end_time: endTime,
                            additional_slots: additionalSlots
                        });
                    }
                });

                // Collect vacation dates
                data.vacations = window.getSelectedVacationDates ? window.getSelectedVacationDates() : [];



                // Collect recurring settings
                const selectedDuration = document.querySelector("input[name='duration']:checked");
                if (selectedDuration) {
                    data.recurring = true;
                    data.duration = selectedDuration.value;
                    if (data.duration === 'custom') {
                        data.customWeeks = document.getElementById("custom-weeks-input")?.value;
                    }
                }



                return data;
            }



            function validateAvailabilityData(data) {
                if (data.days.length === 0) {
                    alert("Please select at least one day.");
                    return false;
                }

                if (data.recurring && !data.duration) {
                    alert("Please select a duration for recurring availability.");
                    return false;
                }

                if (data.duration === 'custom' && (!data.customWeeks || data.customWeeks < 1)) {
                    alert("Please enter a valid number of weeks.");
                    return false;
                }

                return true;
            }
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/business/staffs/modal/available-modal.blade.php ENDPATH**/ ?>