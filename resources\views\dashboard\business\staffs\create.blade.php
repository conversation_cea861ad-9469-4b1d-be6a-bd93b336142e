@extends('dashboard.layout.master')
@push('css')
    <style>
        .error {
            color: #fd011a !important;
            font-weight: bold !important;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }
    </style>
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-coupon padding-block">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 breadcrumbs">
                    <!-- <div> -->
                    <h6 class="sora black">Add Staff Member</h6>
                    <p class="fs-14 sora light-black m-0">Staff Members<span class="mx-3"><i
                                class="fa-solid fa-chevron-right right-arrow"></i></span> Add Staff Member</p>
                </div>
                <div class="col-md-12">
                    <form action="{{ route('staffs.store') }}" method="POST" enctype="multipart/form-data"
                        class="form-add-services" id="staffForm">
                        @csrf
                        <div class="row row-gap-5">
                            <div class="col-md-12">
                                <label for="description" class="form-label form-input-labels">Thumbnail
                                    Image</label>
                                <div class="position-relative ">
                                    <div class="image-input image-input-empty" data-kt-image-input="true">
                                        <div class="image-input-wrapper"></div>
                                        <label
                                            class="image-label  flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                                            data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                            data-bs-dismiss="click" title="Change avatar">
                                            <i class="bi bi-upload upload-icon"></i>
                                            <span>Upload Image</span>
                                            <span>50x50 px</span>

                                            <!--begin::Inputs-->
                                            <input type="file" name="avatar" accept=".png, .jpg, .jpeg" />
                                            <input type="hidden" name="avatar_remove" />
                                            <!--end::Inputs-->
                                        </label>

                                        <span
                                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                            data-bs-dismiss="click" title="Cancel avatar">
                                            <i class="ki-outline ki-cross fs-3"></i>
                                        </span>
                                        <!--end::Cancel button-->

                                        <!--begin::Remove button-->
                                        <span
                                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                            data-bs-dismiss="click" title="Remove avatar">
                                            <i class="ki-outline ki-cross fs-3"></i>
                                        </span>
                                        <!--end::Remove button-->
                                    </div>
                                    <!--end::Image input-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="full-name" class="form-label form-input-labels">Full Name</label>
                                <input type="text" class="form-control form-inputs-field" placeholder="Enter full-name"
                                    id="name" name="name">
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label form-input-labels">Email Address</label>
                                <input type="email" class="form-control form-inputs-field"
                                    placeholder="Enter email address" id="email" name="email">
                            </div>
                            <div class="col-md-6">
                                <label for="category-secondary" class="form-label form-input-labels">Category</label>
                                <select class="form-select form-select-field" data-control="select2"
                                    data-dropdown-css-class="w-619px" data-close-on-select="true"
                                    data-placeholder="Select an option" data-allow-clear="true" id="category-secondary"
                                    name="category_id">
                                    <option></option>
                                    @foreach ($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="subcategory-secondary" class="form-label form-input-labels">Sub
                                    Category</label>
                                <select class="form-select form-select-field" data-control="select2"
                                    data-dropdown-css-class="w-619px" data-close-on-select="true"
                                    data-placeholder="Select a category first" data-allow-clear="true" id="subcategory-secondary"
                                    name="subcategory_id" disabled>
                                    <option></option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="phone" class="form-label form-input-label">Phone Number</label>
                                <div class="form-control form-inputs-field">
                                    <input type="tel" id="phone" class="" name="phone">
                                </div>

                            </div>
                            <div class="col-md-6">
                                <label for="availability" class="form-label form-input-labels">Availability</label>
                                <a class="form-control form-inputs-field d-flex justify-content-between"
                                    data-bs-toggle="modal" data-bs-target="#availabilityModal" id="availability-btn">Select availability<span><i
                                            class="fa-solid fa-chevron-down"></i></span></a>

                                <!-- Hidden inputs to store availability data -->
                                <input type="hidden" name="availability_data" id="availability-data">
                                <input type="hidden" name="vacations_data" id="vacations-data">
                                <input type="hidden" name="recurring_data" id="recurring-data">
                            </div>
                            <div class="col-md-6">
                                <label for="socal-icon-fb" class="form-label form-input-labels">Facebook</label>
                                <input type="text" class="form-control form-inputs-field"
                                    placeholder="Enter facebook link" id="facebook" name="facebook">
                            </div>
                            <div class="col-md-6">
                                <label for="socal-icon-insta" class="form-label form-input-labels">Instagram</label>
                                <input type="text" class="form-control form-inputs-field"
                                    placeholder="Enter instagram link" id="instagram" name="instagram">
                            </div>
                            <div class="col-md-6">
                                <label for="socal-icon-yt" class="form-label form-input-labels">Youtube</label>
                                <input type="text" class="form-control form-inputs-field"
                                    placeholder="Enter youtube link" id="youtube" name="youtube">
                            </div>
                            <div class="col-md-6">
                                <label for="socal-icon-tiktok" class="form-label form-input-labels">Tiktok</label>
                                <input type="text" class="form-control form-inputs-field"
                                    placeholder="Enter tiktok link" id="tiktok" name="tiktok">
                            </div>
                            <div class="col-md-12 mt-6">
                                <button type="submit" class="add-btn">
                                    Add
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.business.staffs.modal.available-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 with icon templates
            $('#social-select').select2({
                templateResult: formatOption,
                templateSelection: formatOption,
                minimumResultsForSearch: Infinity, // remove search
                escapeMarkup: m => m,
                width: '100px'
            });

            function formatOption(state) {
                if (!state.id) return state.text;
                const icon = $(state.element).data('icon');
                return `<i class="${icon}"></i> ${state.text}`;
            }

            // Autofill URL when selected
            $('#social-select').on('change', function() {
                const selected = $(this).find(':selected');
                const url = selected.data('url') || '';
                $('#social-url').val(url);
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                if (element.files.length === 0) {
                    return true; // no file selected, let 'required' rule handle this
                }
                const fileSizeKB = element.files[0].size / 1024; // size in KB
                return fileSizeKB <= maxSizeKB;
            }, 'File size must be less than {0} KB.');

            // Add custom regex validation method (if not already included)
            $.validator.addMethod("pattern", function(value, element, param) {
                if (this.optional(element)) {
                    return true;
                }
                if (typeof param === "string") {
                    param = new RegExp(param);
                }
                return param.test(value);
            }, "Invalid format");

            // Handle category change to populate subcategories
            $('#category-secondary').on('change', function() {
                var categoryId = $(this).val(); // This is the integer ID for both form submission and AJAX
                var subcategorySelect = $('#subcategory-secondary');

                // Clear subcategory dropdown
                subcategorySelect.empty().append('<option></option>');

                if (categoryId) {
                    // Enable subcategory dropdown
                    subcategorySelect.prop('disabled', false);
                    subcategorySelect.attr('data-placeholder', 'Loading subcategories...');

                    // Fetch subcategories via AJAX using the integer ID
                    $.ajax({
                        url: '/subcategories/get-subcategories/' + categoryId,
                        type: 'GET',
                        success: function(response) {
                            console.log('AJAX Response:', response); // Debug log

                            if (response.status && response.data && response.data.length > 0) {
                                // Populate subcategories
                                $.each(response.data, function(index, subcategory) {
                                    console.log('Processing subcategory:', subcategory); // Debug log

                                    // Add null checks
                                    if (subcategory && subcategory.id && subcategory.name) {
                                        subcategorySelect.append('<option value="' + subcategory.id + '">' + subcategory.name + '</option>');
                                    } else {
                                        console.error('Invalid subcategory object:', subcategory);
                                    }
                                });
                                subcategorySelect.attr('data-placeholder', 'Select a subcategory');
                            } else {
                                // No subcategories found
                                console.log('No subcategories found or invalid response');
                                subcategorySelect.attr('data-placeholder', 'No subcategories available');
                            }

                            // Reinitialize Select2 to update placeholder
                            subcategorySelect.select2({
                                dropdownCssClass: 'w-619px',
                                closeOnSelect: true,
                                allowClear: true
                            });
                        },
                        error: function(xhr, status, error) {
                            console.error('Error fetching subcategories:', error);
                            subcategorySelect.attr('data-placeholder', 'Error loading subcategories');
                            subcategorySelect.select2({
                                dropdownCssClass: 'w-619px',
                                closeOnSelect: true,
                                allowClear: true
                            });
                        }
                    });
                } else {
                    // Disable subcategory dropdown when no category is selected
                    subcategorySelect.prop('disabled', true);
                    subcategorySelect.attr('data-placeholder', 'Select a category first');
                    subcategorySelect.select2({
                        dropdownCssClass: 'w-619px',
                        closeOnSelect: true,
                        allowClear: true
                    });
                }
            });

            // Add form validation
            $("#staffForm").validate({
                errorClass: "error",
                errorElement: "span",
                errorPlacement: function(error, element) {
                    error.insertAfter(element);
                },
                rules: {
                    avatar: {
                        required: true,
                        maxFileSize: 5120
                    },
                    name: {
                        required: true,
                        maxlength: 30,
                        pattern: /^[a-zA-Z0-9\s]+$/
                    },
                    email: {
                        required: true,
                        maxlength: 30,
                    },
                    category_id: {
                        required: true
                    },
                    subcategory_id: {
                        required: true
                    },
                    facebook: {
                        pattern: /^(https?:\/\/)?(www\.)?facebook\.com\/[a-zA-Z0-9(\.\?)$]+$/,
                    },
                    instagram: {
                        pattern: /^(https?:\/\/)?(www\.)?instagram\.com\/[a-zA-Z0-9(\.\?)$]+$/,
                    },
                    tiktok: {
                        pattern: /^(https?:\/\/)?(www\.)?tiktok\.com\/[a-zA-Z0-9(\.\?)$]+$/,
                    },
                    youtube: {
                        pattern: /^(https?:\/\/)?(www\.)?youtube\.com\/[a-zA-Z0-9(\.\?)$]+$/,
                    },
                    phone: {
                        required: true,
                        pattern: /^\d{10}$/,
                    }
                },
                messages: {
                    avatar: {
                        required: "Please upload an image",
                        maxFileSize: "Image size must not exceed 5 MB"
                    },
                    name: {
                        required: "Please enter name",
                        maxlength: "Name is too long try something shorter",
                        pattern: "Name can only contain letters, numbers, and spaces"
                    },
                    email: {
                        required: "Please enter email",
                        maxlength: "Email is too long",
                    },
                    category_id: {
                        required: "Please select category"
                    },
                    subcategory_id: {
                        required: "Please select sub category"
                    },
                    facebook: {
                        pattern: "Please enter valid facebook url"
                    },
                    instagram: {
                        pattern: "Please enter valid instagram url"
                    },
                    tiktok: {
                        pattern: "Please enter valid tiktok url"
                    },
                    youtube: {
                        pattern: "Please enter valid youtube url"
                    },
                    phone: {
                        required: "Please enter phone number",
                        pattern: "Please enter valid phone number"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });
        });
    </script>
@endpush
