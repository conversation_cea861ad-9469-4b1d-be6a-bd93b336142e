<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StaffVacation extends Model
{
    use HasFactory;

    protected $fillable = [
        'staff_id',
        'vacation_date',
    ];

    protected $casts = [
        'vacation_date' => 'date',
    ];

    /**
     * Get the staff that owns the vacation.
     */
    public function staff()
    {
        return $this->belongsTo(Staff::class, 'staff_id');
    }

}
