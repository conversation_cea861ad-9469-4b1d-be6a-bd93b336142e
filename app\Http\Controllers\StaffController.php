<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Staff;
use App\Models\StaffAvailability;

use App\Models\StaffVacation;
use App\Models\SubCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class StaffController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $staffs = Staff::paginate(10);
        $categories = Category::all();
        $subcategories = SubCategory::all();
        return view('dashboard.business.staffs.index', compact('staffs','categories','subcategories'));
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::all();
        return view('dashboard.business.staffs.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'avatar' => ['required', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'email' => ['required', 'regex:' . config('constant.email_regex')],
            'category_id' => 'required',
            'subcategory_id' => 'required',
            'phone' => ['required', 'regex:' . config('constant.phone_regex')],
            'facebook' => ['nullable', 'regex:' . config('constant.url_regex')],
            'instagram' => ['nullable', 'regex:' . config('constant.url_regex')],
            'tiktok' => ['nullable', 'regex:' . config('constant.url_regex')],
            'youtube' => ['nullable', 'regex:' . config('constant.url_regex')],
            'availability_data' => 'nullable|json',

            'vacations_data' => 'nullable|json',
            'recurring_data' => 'nullable|json',
        ], [
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'name.required' => 'Please enter name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['input']['max'],
            'email.required' => 'Please enter email',
            'email.regex' => 'Please enter valid email',
            'category_id.required' => 'Please select category',
            'phone.required' => 'Please enter phone number',
            'phone.regex' => 'Please enter valid phone number',
            'subcategory_id.required' => 'Please select sub category',
            'facebook.regex' => 'Please enter valid facebook url',
            'instagram.regex' => 'Please enter valid instagram url',
            'tiktok.regex' => 'Please enter valid tiktok url',
            'youtube.regex' => 'Please enter valid youtube url',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $staffData = $validator->validated();
            $staffData['user_id'] = auth()->user()->id;
            if ($request->hasFile('avatar')) {
                $staffData['image'] = $this->storeImage('staff-images', $request->file('avatar'));
            }
            $staff = Staff::create($staffData);
            // Process availability data if provided
            if ($request->filled('availability_data')) {
                $this->processStaffAvailability($staff->id, $request);
            }
            DB::commit();
            return redirect()->route('staffs.index')->with(["type" => "success", "title" => "Created", "message" => 'Staff Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->route('staffs.index')->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();
        return view('dashboard.business.staffs.show', compact('staff'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();

        // Get availability data
        $availabilities = StaffAvailability::where('staff_id', $staff->id)->get();

        // Get vacation data
        $vacations = StaffVacation::where('staff_id', $staff->id)->pluck('vacation_date')->toArray();

        // Group availabilities by day, then group by unique time patterns
        $availabilityByDay = [];

        // First group by day
        $groupedByDay = $availabilities->groupBy('day');

        foreach ($groupedByDay as $day => $dayAvailabilities) {
            // Then group by unique time patterns within each day
            $uniqueTimeSlots = [];

            foreach ($dayAvailabilities as $availability) {
                $timeSlot = [
                    'start_time' => $availability->start_time,
                    'end_time' => $availability->end_time
                ];

                // Check if this time slot already exists
                $exists = false;
                foreach ($uniqueTimeSlots as $existingSlot) {
                    if ($existingSlot['start_time'] === $timeSlot['start_time'] &&
                        $existingSlot['end_time'] === $timeSlot['end_time']) {
                        $exists = true;
                        break;
                    }
                }

                // Only add if it doesn't exist
                if (!$exists) {
                    $uniqueTimeSlots[] = $timeSlot;
                }
            }

            // Now organize into main_slot and additional_slots
            if (!empty($uniqueTimeSlots)) {
                $availabilityByDay[$day] = [
                    'day' => $day,
                    'main_slot' => $uniqueTimeSlots[0], // First slot is main
                    'additional_slots' => array_slice($uniqueTimeSlots, 1) // Rest are additional
                ];
            }
        }

        // Convert to array format expected by frontend
        $formattedAvailability = array_values($availabilityByDay);

        // Determine if this is recurring by checking if there are multiple weeks of data
        $uniqueDates = $availabilities->pluck('date')->unique();
        $isRecurring = $uniqueDates->count() > 7; // More than one week

        // Calculate duration if recurring
        $duration = null;
        $customWeeks = null;
        if ($isRecurring) {
            $weeksCount = ceil($uniqueDates->count() / 7);
            if ($weeksCount == 4) {
                $duration = '4weeks';
            } elseif ($weeksCount == 8) {
                $duration = '8weeks';
            } else {
                $duration = 'custom';
                $customWeeks = $weeksCount;
            }
        }

        $staff->availability_data = $formattedAvailability;
        $staff->vacations_data = $vacations;
        $staff->recurring_data = [
            'recurring' => $isRecurring,
            'duration' => $duration,
            'customWeeks' => $customWeeks
        ];

        return response()->json($staff);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'avatar' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'email' => ['required', 'regex:' . config('constant.email_regex')],
            'category_id' => 'required',
            'subcategory_id' => 'required',
            'phone' => ['required', 'regex:' . config('constant.phone_regex')],
            'facebook' => ['nullable', 'regex:' . config('constant.url_regex')],
            'instagram' => ['nullable', 'regex:' . config('constant.url_regex')],
            'tiktok' => ['nullable', 'regex:' . config('constant.url_regex')],
            'youtube' => ['nullable', 'regex:' . config('constant.url_regex')],
            'availability_data' => 'nullable|json',
            'vacations_data' => 'nullable|json',
            'recurring_data' => 'nullable|json',
        ], [
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'name.required' => 'Please enter name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['input']['max'],
            'email.required' => 'Please enter email',
            'email.regex' => 'Please enter valid email',
            'category_id.required' => 'Please select category',
            'subcategory_id.required' => 'Please select sub category',
            'phone.required' => 'Please enter phone number',
            'phone.regex' => 'Please enter valid phone number',
            'facebook.regex' => 'Please enter valid facebook url',
            'instagram.regex' => 'Please enter valid instagram url',
            'tiktok.regex' => 'Please enter valid tiktok url',
            'youtube.regex' => 'Please enter valid youtube url',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $staff = Staff::where('ids', $id)->firstOrFail();
            $staffData = $validator->validated();
            if ($request->hasFile('avatar')) {
                $this->deleteImage($staff->image);
                $staffData['image'] = $this->storeImage('staff-images', $request->file('avatar'));
            }
            $staff->update($staffData);

            // Process availability data if provided
            if ($request->filled('availability_data')) {
                $this->processStaffAvailability($staff->id, $request);
            }

            DB::commit();
            return response()->json(["type" => "success", "title" => "Updated", "message" => 'Staff Updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();
        $this->deleteImage($staff->image);
        $staff->delete();
        return response()->json(["type" => "success", "title" => "Deleted", "message" => 'Staff Deleted Successfully!!']);
    }

    public function updateStatus(Request $request)
    {
        $staff = Staff::findOrFail($request->staff_id);
        $staff->status = $request->status;
        $staff->save();

        return response()->json(['success' => true]);
    }



    /**
     * Get day of week number (0 = Monday, 6 = Sunday)
     */
    private function getDayOfWeekNumber($dayName)
    {
        $days = [
            'Monday' => 0,
            'Tuesday' => 1,
            'Wednesday' => 2,
            'Thursday' => 3,
            'Friday' => 4,
            'Saturday' => 5,
            'Sunday' => 6
        ];

        return $days[$dayName] ?? 0;
    }



    /**
     * Process staff availability data from the form
     */
    private function processStaffAvailability($staffId, Request $request)
    {
        $availabilityData = json_decode($request->availability_data, true);
        $vacationsData = json_decode($request->vacations_data, true);
        $recurringData = json_decode($request->recurring_data, true);

        // Delete existing vacation records for this staff
        StaffVacation::where('staff_id', $staffId)->delete();

        // Save vacation days
        if (!empty($vacationsData)) {
            foreach ($vacationsData as $vacationDate) {
                StaffVacation::create([
                    'staff_id' => $staffId,
                    'vacation_date' => $vacationDate
                ]);
            }
        }
        // Calculate the number of weeks to generate
        $weeksToGenerate = 1; // Default to 1 week
        if ($recurringData && $recurringData['recurring']) {
            $weeksToGenerate = $this->calculateWeeksFromRecurring($recurringData);
        }
        // Generate availability records
        if (!empty($availabilityData)) {
            $this->generateStaffAvailabilityRecords($staffId, $availabilityData, $weeksToGenerate);
        }
    }
    /**
     * Calculate weeks from recurring data
     */
    private function calculateWeeksFromRecurring($recurringData)
    {
        switch ($recurringData['duration']) {
            case '4':
                return 4;
            case '8':
                return 8;
            case 'custom':
                return (int) $recurringData['customWeeks'];
            default:
                return 1;
        }
    }

    /**
     * Generate staff availability records
     */
    private function generateStaffAvailabilityRecords($staffId, $availabilityData, $weeksToGenerate)
    {
        $startDate = Carbon::now()->startOfWeek(); // Start from current week's Monday

        // Delete existing availability records for this staff to avoid duplicates
        StaffAvailability::where('staff_id', $staffId)->delete();

        for ($week = 0; $week < $weeksToGenerate; $week++) {
            foreach ($availabilityData as $dayData) {
                $dayOfWeek = $this->getDayOfWeekNumber($dayData['day']);
                $currentDate = $startDate->copy()->addWeeks($week)->addDays($dayOfWeek);

                // Always create availability records (don't skip vacation days)
                // Create main availability record
                StaffAvailability::create([
                    'staff_id' => $staffId,
                    'date' => $currentDate->format('Y-m-d'),
                    'day' => $dayData['day'],
                    'start_time' => $dayData['start_time'],
                    'end_time' => $dayData['end_time']
                ]);

                // Create additional time slots if they exist
                if (!empty($dayData['additional_slots'])) {
                    foreach ($dayData['additional_slots'] as $slot) {
                        StaffAvailability::create([
                            'staff_id' => $staffId,
                            'date' => $currentDate->format('Y-m-d'),
                            'day' => $dayData['day'],
                            'start_time' => $slot['start_time'],
                            'end_time' => $slot['end_time']
                        ]);
                    }
                }
            }
        }
    }
}
