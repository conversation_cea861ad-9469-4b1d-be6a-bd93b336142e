<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Staff;
use App\Models\StaffAvailability;

use App\Models\StaffVacation;
use App\Models\SubCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class StaffController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $staffs = Staff::paginate(10);
        $categories = Category::all();
        $subcategories = SubCategory::all();
        return view('dashboard.business.staffs.index', compact('staffs','categories','subcategories'));
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::all();
        return view('dashboard.business.staffs.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'avatar' => ['required', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'email' => ['required', 'regex:' . config('constant.email_regex')],
            'category_id' => 'required',
            'subcategory_id' => 'required',
            'facebook' => ['nullable', 'regex:' . config('constant.url_regex')],
            'instagram' => ['nullable', 'regex:' . config('constant.url_regex')],
            'tiktok' => ['nullable', 'regex:' . config('constant.url_regex')],
            'youtube' => ['nullable', 'regex:' . config('constant.url_regex')],
            'availability_data' => 'nullable|json',

            'vacations_data' => 'nullable|json',
            'recurring_data' => 'nullable|json',
        ], [
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'name.required' => 'Please enter name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['input']['max'],
            'email.required' => 'Please enter email',
            'email.regex' => 'Please enter valid email',
            'category_id.required' => 'Please select category',
            'subcategory_id.required' => 'Please select sub category',
            'facebook.regex' => 'Please enter valid facebook url',
            'instagram.regex' => 'Please enter valid instagram url',
            'tiktok.regex' => 'Please enter valid tiktok url',
            'youtube.regex' => 'Please enter valid youtube url',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $staffData = $validator->validated();
            $staffData['user_id'] = auth()->user()->id;
            if ($request->hasFile('avatar')) {
                $staffData['image'] = $this->storeImage('staff-images', $request->file('avatar'));
            }
            $staff = Staff::create($staffData);
            // Process availability data if provided
            if ($request->filled('availability_data')) {
                $this->processStaffAvailability($staff->id, $request);
            }
            DB::commit();
            return redirect()->route('staffs.index')->with(["type" => "success", "title" => "Created", "message" => 'Staff Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->route('staffs.index')->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();
        return view('dashboard.business.staffs.show', compact('staff'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();
        return response()->json($staff);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'avatar' => ['required', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'email' => ['required', 'regex:' . config('constant.email_regex')],
            'category_id' => 'required',
            'subcategory_id' => 'required',
            'facebook' => ['nullable', 'regex:' . config('constant.url_regex')],
            'instagram' => ['nullable', 'regex:' . config('constant.url_regex')],
            'tiktok' => ['nullable', 'regex:' . config('constant.url_regex')],
            'youtube' => ['nullable', 'regex:' . config('constant.url_regex')],
        ], [
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'name.required' => 'Please enter name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['input']['max'],
            'email.required' => 'Please enter email',
            'email.regex' => 'Please enter valid email',
            'category_id.required' => 'Please select category',
            'subcategory_id.required' => 'Please select sub category',
            'facebook.regex' => 'Please enter valid facebook url',
            'instagram.regex' => 'Please enter valid instagram url',
            'tiktok.regex' => 'Please enter valid tiktok url',
            'youtube.regex' => 'Please enter valid youtube url',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $staff = Staff::where('ids', $id)->firstOrFail();
            $staffData = $validator->validated();
            if ($request->hasFile('avatar')) {
                $this->deleteImage($staff->image);
                $staffData['image'] = $this->storeImage('staff-images', $request->file('avatar'));
            }
            $staff->update($staffData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Updated", "message" => 'Staff Updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();
        $this->deleteImage($staff->image);
        $staff->delete();
        return response()->json(["type" => "success", "title" => "Deleted", "message" => 'Staff Deleted Successfully!!']);
    }

    public function updateStatus(Request $request)
    {
        $staff = Staff::findOrFail($request->staff_id);
        $staff->status = $request->status;
        $staff->save();

        return response()->json(['success' => true]);
    }



    /**
     * Get day of week number (0 = Monday, 6 = Sunday)
     */
    private function getDayOfWeekNumber($dayName)
    {
        $days = [
            'Monday' => 0,
            'Tuesday' => 1,
            'Wednesday' => 2,
            'Thursday' => 3,
            'Friday' => 4,
            'Saturday' => 5,
            'Sunday' => 6
        ];

        return $days[$dayName] ?? 0;
    }

    /**
     * Process staff availability data from the form
     */
    private function processStaffAvailability($staffId, Request $request)
    {
        $availabilityData = json_decode($request->availability_data, true);
        $vacationsData = json_decode($request->vacations_data, true);
        $recurringData = json_decode($request->recurring_data, true);

        // Delete existing vacation records for this staff
        StaffVacation::where('staff_id', $staffId)->delete();

        // Save vacation days
        if (!empty($vacationsData)) {
            foreach ($vacationsData as $vacationDate) {
                StaffVacation::create([
                    'staff_id' => $staffId,
                    'vacation_date' => $vacationDate
                ]);
            }
        }
        // Calculate the number of weeks to generate
        $weeksToGenerate = 1; // Default to 1 week
        if ($recurringData && $recurringData['recurring']) {
            $weeksToGenerate = $this->calculateWeeksFromRecurring($recurringData);
        }
        // Generate availability records
        if (!empty($availabilityData)) {
            $this->generateStaffAvailabilityRecords($staffId, $availabilityData, $weeksToGenerate);
        }
    }
    /**
     * Calculate weeks from recurring data
     */
    private function calculateWeeksFromRecurring($recurringData)
    {
        switch ($recurringData['duration']) {
            case '4':
                return 4;
            case '8':
                return 8;
            case 'custom':
                return (int) $recurringData['customWeeks'];
            default:
                return 1;
        }
    }

    /**
     * Generate staff availability records
     */
    private function generateStaffAvailabilityRecords($staffId, $availabilityData, $weeksToGenerate)
    {
        $startDate = Carbon::now()->startOfWeek(); // Start from current week's Monday

        // Delete existing availability records for this staff to avoid duplicates
        StaffAvailability::where('staff_id', $staffId)->delete();

        for ($week = 0; $week < $weeksToGenerate; $week++) {
            foreach ($availabilityData as $dayData) {
                $dayOfWeek = $this->getDayOfWeekNumber($dayData['day']);
                $currentDate = $startDate->copy()->addWeeks($week)->addDays($dayOfWeek);

                // Always create availability records (don't skip vacation days)
                // Create main availability record
                StaffAvailability::create([
                    'staff_id' => $staffId,
                    'date' => $currentDate->format('Y-m-d'),
                    'day' => $dayData['day'],
                    'start_time' => $dayData['start_time'],
                    'end_time' => $dayData['end_time']
                ]);

                // Create additional time slots if they exist
                if (!empty($dayData['additional_slots'])) {
                    foreach ($dayData['additional_slots'] as $slot) {
                        StaffAvailability::create([
                            'staff_id' => $staffId,
                            'date' => $currentDate->format('Y-m-d'),
                            'day' => $dayData['day'],
                            'start_time' => $slot['start_time'],
                            'end_time' => $slot['end_time']
                        ]);
                    }
                }
            }
        }
    }
}
