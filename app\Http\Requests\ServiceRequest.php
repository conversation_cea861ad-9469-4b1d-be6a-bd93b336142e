<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ServiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->hasRole(["professional", "individual", "business"]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $rules = [];
        if ($this->type == "group") {
            $rules = [
                "name" => "required|string|max:225",
                "category_id" => "required|exists:categories,ids",
                "subcategory_id" => "required|exists:sub_categories,ids",
                "duration" => "required|integer",
                "availabilities_dates" => "required|json",
                "discount_recurring" => "sometimes|numeric|min:0|max:100",
                "total_slots" => "required|integer|min:1",
                "price_per_slot" => "required|numeric|min:0",
                "additional_service_tax" => "sometimes|numeric|min:0",
                "required_items" => "sometimes|string|max:1000",
                "description" => "sometimes|string",

                "service_location" => "required|in:onsite,outside-location",
                "physical_location" => "required_if:service_location,onsite|max:225",
                "outside_location" => "required_if:service_location,outside-location|max:225",
                "staff_ids" => "sometimes|array",
                "staff_ids.*" => "exists:staffs,id",
            ];
        }elseif($this->type == "individual"){
            $rules = [
                "name" => "required|string|max:225",
                "category_id" => "required|exists:categories,ids",
                "subcategory_id" => "required|exists:sub_categories,ids",
                "duration" => "required|integer",
                "price" => "required|numeric|min:0",
                "additional_cost" => "sometimes|numeric|min:0",
                "availabilities_dates" => "required|json",
                "required_items" => "sometimes|string|max:1000",
                "description" => "sometimes|string",
                "is_onsite" => "sometimes|boolean",
                "physical_location" => "required_if:is_onsite,1|max:225",
                "is_customer_location" => "sometimes|boolean",
                "radius" => "required_if:is_customer_location,1|numeric|min:0",
                "travel_time" => "required_if:is_customer_location,1|numeric|min:0",
                "service_charges" => "required_if:is_customer_location,1|numeric|min:0",
                "staff_ids" => "sometimes|array",
                "staff_ids.*" => "exists:staffs,id",
            ];
        }
        if ($this->method() == "POST") {
            $rules["thumbnail"] = "required|image|mimes:jpeg,png,jpg|max:2048";
        }
        abort_if(empty($rules), 400, "Invalid Service Type");
        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    // public function messages()
    // {
    //     return [
    //         'category_id.exists' => 'This email is already taken.',
    //     ];
    // }
    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'category_id' => 'category',
            'subcategory_id' => 'sub category',
        ];
    }
}
