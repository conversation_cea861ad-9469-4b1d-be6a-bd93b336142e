@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Services</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    <a href="{{ route('services.create', ['type' => 'individual']) }}" class="add-btn">
                        <i class="fa-solid fa-plus me-3"></i> Add Service
                    </a>
                </div>
                <div class="col-md-12">
                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active business-services" id="pills-services-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-services" type="button" role="tab"
                                aria-controls="pills-services" aria-selected="true">
                                Services
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link business-services" id="pills-classes-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-classes" type="button" role="tab" aria-controls="pills-classes"
                                aria-selected="true">
                                Classes
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-services" role="tabpanel"
                            aria-labelledby="pills-services-tab" tabindex="0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="table-container">
                                        <div class="table_top d-flex gap-4 align-items-center">
                                            <div class="search_box">
                                                <label for="customSearchInput">
                                                    <i class="fas fa-search"></i>
                                                </label>
                                                <input class="search_input search" type="text" id="customSearchInput"
                                                    placeholder="Search..." />
                                            </div>
                                            <!-- Select with dots -->
                                            <div class="dropdown search_box select-box">
                                                <button
                                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <span><span class="dot"></span>
                                                        All</span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="All" data-color="#4B5563"><span
                                                                class="dot all"></span>
                                                            All</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Ongoing" data-color="#F59E0B"><span
                                                                class="dot ongoing"></span>
                                                            Ongoing</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Upcoming" data-color="#3B82F6"><span
                                                                class="dot upcoming"></span>
                                                            Upcoming</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Complete" data-color="#10B981"><span
                                                                class="dot completed"></span>
                                                            Complete</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Canceled" data-color="#EF4444"><span
                                                                class="dot cancelled-dot"></span>
                                                            Canceled</a></li>
                                                </ul>
                                            </div>
                                            <!-- category -->
                                            <div class="search_box select-box">
                                                <select class="search_input">
                                                    <option value="Category">Category</option>
                                                    <option value="all">All</option>
                                                    <option value=" Group"> Group</option>
                                                    <option value="Individual">Individual</option>
                                                </select>
                                            </div>
                                        </div>
                                        <table id="responsiveTable"
                                            class="responsiveTable display wallet-history-table w-100">
                                            <thead>
                                                <tr>
                                                    <th></th>
                                                    <th>Category</th>
                                                    <th>Duration</th>
                                                    <th>Price</th>
                                                    <th>Assigned Staff</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse ($individual_services as $individual_service)
                                                    <tr>
                                                        <td data-label="" class="w-400px">
                                                            <div class="card  flex-row shadow-none p-0 gap-3">
                                                                <div class="card-header p-0 border-0 align-items-start">
                                                                    <img src="{{ asset('website') . '/' . $individual_service->image }}"
                                                                        class="h-80px w-80px rounded-3 object-fit-contain"
                                                                        alt="card-image" />
                                                                </div>
                                                                <div class="card-body p-0">
                                                                    <p class="fs-16 regular black">
                                                                        {{ $individual_service->name }}</p>
                                                                    <p class="light-black opacity-6 fs-14 normal">
                                                                        {{ $individual_service->description }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td data-label="Category">
                                                            {{ $individual_service->category->name ?? '-' }}
                                                        </td>
                                                        <td data-label="Duration">
                                                            {{ $individual_service->duration . ' min' }}</td>
                                                        <td data-label="Price">${{ $individual_service->price }}</td>
                                                        <td data-label="Assigned Staff">
                                                            @if($individual_service->staff && $individual_service->staff->count() > 0)
                                                                @foreach($individual_service->staff as $staff)
                                                                    <span class="badge bg-primary me-1">{{ $staff->name }}</span>
                                                                @endforeach
                                                            @else
                                                                <span class="text-muted">No staff assigned</span>
                                                            @endif
                                                        </td>
                                                        <td data-label="Action">
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button"
                                                                    id="dropdownMenuButton" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton">
                                                                    <li>
                                                                        <a href="{{ route('services.edit', ['service' => $individual_service->ids, 'type' => $individual_service->type]) }}"
                                                                            class="dropdown-item complete fs-14 regular ">
                                                                            <i
                                                                                class="bi bi-check-circle complete-icon"></i>
                                                                            Edit
                                                                        </a>
                                                                    </li>
                                                                    <li>
                                                                        <form
                                                                            action="{{ route('services.destroy', $individual_service->ids) }}"
                                                                            method="POST" class="delete-form">
                                                                            @csrf
                                                                            @method('DELETE')
                                                                            <button
                                                                                class="dropdown-item cancel fs-14 regular"
                                                                                type="button" onclick="showDeleteConfirmation(this)">
                                                                                <i
                                                                                    class="fa-solid fa-xmark cancel-icon"></i>
                                                                                Delete
                                                                            </button>
                                                                        </form>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @empty
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-classes" role="tabpanel"
                            aria-labelledby="pills-classes-tab" tabindex="0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="table-container">
                                        <div class="table_top d-flex gap-4 align-items-center">
                                            <div class="search_box">
                                                <label for="customSearchInput">
                                                    <i class="fas fa-search"></i>
                                                </label>
                                                <input class="search_input search" type="text" id="customSearchInput"
                                                    placeholder="Search..." />
                                            </div>

                                            <!-- Select with dots -->
                                            <div class="dropdown search_box select-box">
                                                <button
                                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <span><span class="dot"></span>
                                                        All</span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="All" data-color="#4B5563"><span
                                                                class="dot all"></span>
                                                            All</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Ongoing" data-color="#F59E0B"><span
                                                                class="dot ongoing"></span>
                                                            Ongoing</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Upcoming" data-color="#3B82F6"><span
                                                                class="dot upcoming"></span>
                                                            Upcoming</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Complete" data-color="#10B981"><span
                                                                class="dot completed"></span>
                                                            Complete</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Canceled" data-color="#EF4444"><span
                                                                class="dot cancelled-dot"></span>
                                                            Canceled</a></li>
                                                </ul>
                                            </div>
                                            <!-- category -->
                                            <div class="search_box select-box">
                                                <select class="search_input">
                                                    <option value="Category">Category</option>
                                                    <option value="all">All</option>
                                                    <option value=" Group"> Group</option>
                                                    <option value="Individual">Individual</option>
                                                </select>
                                            </div>

                                        </div>
                                        <table id=""
                                            class=" display wallet-history-table w-100 ">
                                            <thead>
                                                <tr>
                                                    <th></th>
                                                    <th>Category</th>
                                                    <th>Timmings</th>
                                                    <th>Days</th>
                                                    <th> Slots</th>
                                                    <th>Price Per Slots</th>
                                                    <th>Assigned Staff</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse ($group_services as $group_service)
                                                    <tr>
                                                        <td data-label="" class="w-400px">
                                                            <div class="card flex-row shadow-none p-0 gap-3">
                                                                <div class="card-header p-0 border-0 align-items-start">
                                                                    <img src="{{ asset('website') . '/' . $group_service->image }}"
                                                                        class="h-80px w-80px rounded-3 object-fit-contain"
                                                                        alt="card-image" />
                                                                </div>
                                                                <div class="card-body p-0">
                                                                    <p class="fs-16 regular black">
                                                                        {{ $group_service->name }}</p>
                                                                    <p class="light-black opacity-6 fs-14 normal">
                                                                        {{ $group_service->description }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td data-label="Category">
                                                            {{ $group_service->category->name ?? '-' }}</td>
                                                        <td data-label="Timmings">{{ $group_service->start_time }} -
                                                            {{ $group_service->end_time }}</td>
                                                        <td data-label="Days">{{ $group_service->days }}</td>
                                                        <td data-label="Slots">{{ $group_service->total_slots }}</td>
                                                        <td data-label="Price Per Slots">${{ $group_service->price }}</td>
                                                        <td data-label="Assigned Staff">
                                                            @if($group_service->staff && $group_service->staff->count() > 0)
                                                                @foreach($group_service->staff as $staff)
                                                                    <span class="badge bg-primary me-1">{{ $staff->name }}</span>
                                                                @endforeach
                                                            @else
                                                                <span class="text-muted">No staff assigned</span>
                                                            @endif
                                                        </td>
                                                        <td data-label="Action">
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button"
                                                                    id="dropdownMenuButton" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton">
                                                                    <li>
                                                                        <a href="{{ route('services.edit', ['service' => $group_service->ids, 'type' => $group_service->type]) }}"
                                                                            class="dropdown-item complete fs-14 regular ">
                                                                            <i
                                                                                class="bi bi-check-circle complete-icon"></i>
                                                                            Edit
                                                                        </a>
                                                                    </li>
                                                                    <li>
                                                                        <form
                                                                            action="{{ route('services.destroy', $group_service->ids) }}"
                                                                            method="POST" class="delete-form">
                                                                            @csrf
                                                                            @method('DELETE')
                                                                            <button
                                                                                class="dropdown-item cancel fs-14 regular"
                                                                                type="button" onclick="showDeleteConfirmation(this)">
                                                                                <i
                                                                                    class="fa-solid fa-xmark cancel-icon"></i>
                                                                                Delete
                                                                            </button>
                                                                        </form>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>

                                                @empty
                                                    <tr>
                                                        <td colspan="7" class="text-center">No Group Services Found
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

