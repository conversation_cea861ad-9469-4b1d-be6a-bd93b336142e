<?php

namespace App\Http\Controllers;

use App\Http\Requests\ServiceRequest;
use App\Models\Category;
use App\Models\Staff;
use App\Services\UserService;

class ServiceController extends Controller
{
    public $userService;
    function __construct()
    {
        $this->userService = new UserService();
    }

    function index()
    {
        $individual_services = $this->userService->getServices(userId: auth()->id(), type: 'individual', withRelations: true);
        $group_services = $this->userService->getServices(userId: auth()->id(), type: 'group', withRelations: true);
        return view('dashboard.service.index', compact('individual_services', 'group_services'));
    }

    function create($type)
    {
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $categories = Category::active()->get();
        $staffs = Staff::all();
        return view('dashboard.service.create', compact('type', 'categories', 'staffs'));
    }

    function store(ServiceRequest $request, $type)
    {
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $service = $this->userService->createService(request_data: $request, type: $type);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service creation failed', 'title' => 'Error']);
        }
        return redirect()->route('services.index')->with(['type' => 'success', 'message' => 'Service created successfully', 'title' => 'Created']);
    }

    function edit($ids, $type)
    {
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $service = $this->userService->getSingleService(service_ids: $ids);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service not found', 'title' => 'Error']);
        }
        $categories = Category::active()->get();
        $staffs = Staff::where('user_id', auth()->id())->get();
        return view('dashboard.service.edit', compact('service', 'type', 'categories', 'staffs'));
    }

    function update(ServiceRequest $request, $ids, $type)
    {
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $service = $this->userService->updateService(request_data: $request, service_ids: $ids, type: $type);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service update failed', 'title' => 'Error']);
        }
        return redirect()->route('services.index')->with(['type' => 'success', 'message' => 'Service updated successfully', 'title' => 'Updated']);
    }

    public function getSubcategories($categoryId)
    {
        try {
            $category = Category::where('ids', $categoryId)->first();
            if (!$category) {
                return response()->json(['success' => false, 'message' => 'Category not found']);
            }

            $subcategories = $category->subcategories()->active()->get(['ids', 'name']);
            return response()->json(['success' => true, 'subcategories' => $subcategories]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error fetching subcategories']);
        }
    }

    function destroy($ids)
    {
        $del_service = $this->userService->deleteService(service_ids: $ids);
        if (!$del_service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service deletion failed', 'title' => 'Error']);
        }
        return redirect()->back()->with(['type' => 'success', 'message' => 'Service deleted successfully', 'title' => 'Deleted']);
    }

}
