<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Staff Members</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    <a href="<?php echo e(route('staffs.create')); ?>" class="add-btn">
                        <i class="fa-solid fa-plus me-3"></i> Add Member
                    </a>
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="customSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="customSearchInput"
                                    placeholder="Search..." />
                            </div>
                            <!-- Select with dots -->
                            <div class="dropdown search_box select-box">
                                <button
                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <span><span class="dot"></span>
                                        All</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                            data-color="#4B5563"><span class="dot all"></span>
                                            All</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Active"
                                            data-color="#F59E0B"><span class="dot ongoing"></span>
                                            Active</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Deactive"
                                            data-color="#3B82F6"><span class="dot upcoming"></span>
                                            Deactive</a></li>
                                </ul>
                            </div>
                            
                        </div>
                        <table id="responsiveTable" class="display w-100">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>Email Address</th>
                                    <th>Category</th>
                                    <th>Bookings</th>
                                    <th>Action</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $staffs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $staff): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td data-label="">
                                            <div class="card  flex-row shadow-none p-0 gap-3 align-items-center">
                                                <div class="card-header p-0 border-0 align-items-start">
                                                    <img src="<?php echo e(asset('website') . '/' . $staff->image); ?>"
                                                        class="h-80px w-80px rounded-3 object-fit-contain"
                                                        alt="card-image" />
                                                </div>
                                                <div class="card-body p-0 ">
                                                    <p class="fs-16 regular black m-0"><?php echo e($staff->name ?? ''); ?></p>
                                                </div>
                                            </div>

                                        </td>
                                        <td data-label="Email Address"><?php echo e($staff->email ?? ''); ?></td>
                                        <td data-label="Category"><?php echo e($staff->category->name ?? ''); ?></td>
                                        <td data-label="Bookings  Per month">20% </td>
                                        <td data-label="">
                                            <div class="toggle-container">
                                                <label class="switch">
                                                    <!-- Dynamically set checked based on category status -->
                                                    <input type="checkbox" class="toggle-input staff-toggle"
                                                        data-staff-id="<?php echo e($staff->id); ?>"
                                                        <?php echo e($staff->status == 1 ? 'checked' : ''); ?>>
                                                    <span class="slider"></span>
                                                </label>
                                                <span
                                                    class="toggle-label"><?php echo e($staff->status == 1 ? 'Active' : 'Deactive'); ?></span>
                                            </div>
                                        </td>
                                        <td data-label="">
                                            <div class="dropdown">
                                                <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <a href="<?php echo e(route('staffs.show', $staff->ids)); ?>"
                                                            class="dropdown-item complete fs-14 regular " type="button">
                                                            <i class="fa-solid fa-eye complete-icon"></i>
                                                            View
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <button class="dropdown-item deep-blue fs-14 regular edit-staff" type="button"
                                                            data-bs-toggle="modal" data-bs-target="#editStaffModal" data-id="<?php echo e($staff->ids); ?>">
                                                            <i class="fa-solid fa-pen-to-square"></i>
                                                            Edit
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center">No Staff Found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        <div class="d-flex justify-content-center" id="discountCouponsPagination"
                            style="<?php echo e($staffs->hasPages() ? '' : 'display: none;'); ?>">
                            <?php echo e($staffs->links('pagination::bootstrap-4')); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php echo $__env->make('dashboard.business.staffs.modal.edit-staff-member', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('dashboard.business.staffs.modal.available-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        // stacked modal in edit
        var elements = Array.prototype.slice.call(document.querySelectorAll("[data-bs-stacked-modal]"));

        if (elements && elements.length > 0) {
            elements.forEach((element) => {
                if (element.getAttribute("data-kt-initialized") === "1") {
                    return;
                }
                element.setAttribute("data-kt-initialized", "1");
                element.addEventListener("click", function(e) {
                    e.preventDefault();

                    const modalEl = document.querySelector(this.getAttribute("data-bs-stacked-modal"));

                    if (modalEl) {
                        const modal = new bootstrap.Modal(modalEl);
                        modal.show();
                    }
                });
            });
        }


        // staff member vacational modal triggerd
        document.querySelectorAll('.toggle-container').forEach(function(container) {
            container.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent dropdown from closing
            });
        });

        // Handle toggle label update and modal trigger
        document.querySelectorAll('.toggle-input-vacation').forEach(function(toggleInput) {
            const container = toggleInput.closest('.toggle-container');
            const toggleLabel = container.querySelector('.toggle-label-vacation');

            toggleInput.addEventListener('change', function() {
                const isOn = toggleInput.checked;
                toggleLabel.textContent = isOn ? 'Vacation On' : 'Vacation Off';

                // Optional: Show modal when vacation is turned ON
                if (isOn) {
                    const modalEl = document.getElementById('vacationModal');
                    console.log("modal opened")
                    if (modalEl) {
                        const modal = new bootstrap.Modal(modalEl);
                        modal.show();
                    }
                }
            });
        });
    </script>

    <script>
        $('.staff-toggle').off('change').on('change', function() {
            const staffId = $(this).data('staff-id');
            const status = $(this).is(':checked') ? 1 : 0;

            $.ajax({
                url: '<?php echo e(route('staffs.update-status')); ?>',
                method: 'POST',
                data: {
                    staff_id: staffId,
                    status: status,
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        // Update toggle label
                        const label = status ? 'Active' : 'Deactive';
                        $(this).siblings('.toggle-label').text(label);
                    }
                }.bind(this)
            });
        });

        $(document).ready(function() {
            // Add validation CSS
            $('<style>.error { color: #fd011a !important; font-weight: bold !important; font-size: 12px; margin-top: 5px; display: block; }</style>').appendTo('head');

            // Custom validation methods
            $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                if (element.files.length === 0) {
                    return true;
                }
                const fileSizeKB = element.files[0].size / 1024;
                return fileSizeKB <= maxSizeKB;
            }, 'File size must be less than {0} KB.');

            $.validator.addMethod("pattern", function(value, element, param) {
                if (this.optional(element)) {
                    return true;
                }
                if (typeof param === "string") {
                    param = new RegExp(param);
                }
                return param.test(value);
            }, "Invalid format");

            // Edit staff click handler
            $('.edit-staff').click(function() {
                var staffId = $(this).data('id');
                $.ajax({
                    url: 'staffs/' + staffId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        // Populate form fields with edit- prefixed IDs
                        $('#edit-staff-id').val(staffId);
                        $('#edit-name').val(data.name);
                        $('#edit-email').val(data.email);
                        $('#edit-category_id').val(data.category_id).trigger('change');
                        $('#edit-phone').val(data.phone);
                        $('#edit-facebook').val(data.facebook);
                        $('#edit-instagram').val(data.instagram);
                        $('#edit-youtube').val(data.youtube);
                        $('#edit-tiktok').val(data.tiktok);

                        // Handle image display
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') || '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            var imageInput = $('#editStaffModal .image-input[data-kt-image-input="true"]');
                            var wrapper = imageInput.find('.image-input-wrapper');
                            wrapper.css('background-image', 'url(' + imageUrl + ')');
                            imageInput.removeClass('image-input-empty').addClass('image-input-changed');
                            imageInput.find('[data-kt-image-input-action="remove"]').removeClass('d-none');
                            imageInput.find('[data-kt-image-input-action="cancel"]').removeClass('d-none');
                        } else {
                            var imageInput = $('#editStaffModal .image-input[data-kt-image-input="true"]');
                            imageInput.addClass('image-input-empty').removeClass('image-input-changed');
                            imageInput.find('.image-input-wrapper').css('background-image', 'none');
                        }

                        // Load subcategories for the selected category
                        if (data.category_id) {
                            loadSubcategoriesForEdit(data.category_id, data.subcategory_id);
                        }

                        // Populate availability data if exists
                        if (data.availability_data && data.availability_data.length > 0) {
                            populateAvailabilityData(data.availability_data, data.vacations_data || [], data.recurring_data || {});
                            $('#edit-availability-btn').html('Availability Set <span><i class="fa-solid fa-check text-success"></i></span>').addClass('text-success');
                        } else {
                            // Reset availability button
                            $('#edit-availability-btn').html('Select availability<span><i class="fa-solid fa-chevron-down"></i></span>').removeClass('text-success');
                            clearAvailabilityData();
                        }

                        $('#editStaffModal').modal('show');
                    },
                });
            });

            // Function to load subcategories for edit modal
            function loadSubcategoriesForEdit(categoryId, selectedSubcategoryId = null) {
                var subcategorySelect = $('#edit-subcategory_id');
                subcategorySelect.empty().append('<option></option>');
                subcategorySelect.prop('disabled', false);
                subcategorySelect.attr('data-placeholder', 'Loading subcategories...');

                $.ajax({
                    url: '/subcategories/get-subcategories/' + categoryId,
                    type: 'GET',
                    success: function(response) {
                        if (response.status && response.data && response.data.length > 0) {
                            $.each(response.data, function(index, subcategory) {
                                if (subcategory && subcategory.id && subcategory.name) {
                                    var selected = selectedSubcategoryId == subcategory.id ? 'selected' : '';
                                    subcategorySelect.append('<option value="' + subcategory.id + '" ' + selected + '>' + subcategory.name + '</option>');
                                }
                            });
                            subcategorySelect.attr('data-placeholder', 'Select a subcategory');
                        } else {
                            subcategorySelect.attr('data-placeholder', 'No subcategories available');
                        }
                        subcategorySelect.select2({
                            dropdownCssClass: 'w-619px',
                            closeOnSelect: true,
                            allowClear: true
                        });
                    },
                    error: function() {
                        subcategorySelect.attr('data-placeholder', 'Error loading subcategories');
                        subcategorySelect.select2({
                            dropdownCssClass: 'w-619px',
                            closeOnSelect: true,
                            allowClear: true
                        });
                    }
                });
            }

            // Handle category change in edit modal
            $('#edit-category_id').on('change', function() {
                var categoryId = $(this).val();
                var subcategorySelect = $('#edit-subcategory_id');

                subcategorySelect.empty().append('<option></option>');

                if (categoryId) {
                    loadSubcategoriesForEdit(categoryId);
                } else {
                    subcategorySelect.prop('disabled', true);
                    subcategorySelect.attr('data-placeholder', 'Select a category first');
                    subcategorySelect.select2({
                        dropdownCssClass: 'w-619px',
                        closeOnSelect: true,
                        allowClear: true
                    });
                }
            });

            // Edit form validation and AJAX submission
            $("#editStaffForm").validate({
                errorClass: "error",
                errorElement: "span",
                errorPlacement: function(error, element) {
                    error.insertAfter(element);
                },
                rules: {
                    avatar: {
                        maxFileSize: 5120
                    },
                    name: {
                        required: true,
                        maxlength: 30,
                        pattern: /^[a-zA-Z0-9\s]+$/
                    },
                    email: {
                        required: true,
                        maxlength: 30,
                    },
                    category_id: {
                        required: true
                    },
                    subcategory_id: {
                        required: true
                    },
                    facebook: {
                        pattern: /^(https?:\/\/)?(www\.)?facebook\.com\/[a-zA-Z0-9(\.\?)$]+$/,
                    },
                    instagram: {
                        pattern: /^(https?:\/\/)?(www\.)?instagram\.com\/[a-zA-Z0-9(\.\?)$]+$/,
                    },
                    tiktok: {
                        pattern: /^(https?:\/\/)?(www\.)?tiktok\.com\/@[a-zA-Z0-9(\.\?)$]+$/,
                    },
                    youtube: {
                        pattern: /^(https?:\/\/)?(www\.)?youtube\.com\/[a-zA-Z0-9(\.\?)$]+$/,
                    },
                    phone: {
                        required: true,
                        pattern: /^[0-9+\-\s()]+$/
                    }
                },
                messages: {
                    avatar: {
                        maxFileSize: "Image size must not exceed 5 MB"
                    },
                    name: {
                        required: "Please enter staff name",
                        maxlength: "Staff name is too long try something shorter",
                        pattern: "Staff name can only contain letters, numbers, and spaces"
                    },
                    email: {
                        required: "Please enter email",
                        maxlength: "Email is too long",
                    },
                    category_id: {
                        required: "Please select category"
                    },
                    subcategory_id: {
                        required: "Please select sub category"
                    },
                    facebook: {
                        pattern: "Please enter valid facebook url"
                    },
                    instagram: {
                        pattern: "Please enter valid instagram url"
                    },
                    tiktok: {
                        pattern: "Please enter valid tiktok url"
                    },
                    youtube: {
                        pattern: "Please enter valid youtube url"
                    },
                    phone: {
                        required: "Please enter phone number",
                        pattern: "Please enter valid phone number"
                    }
                },
                submitHandler: function(form) {
                    var staffId = $('#edit-staff-id').val();
                    var formData = new FormData(form);
                    $.ajax({
                        url: '/staffs/' + staffId,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#editStaffModal').modal('hide');
                            Swal.fire({
                                icon: response.type,
                                title: response.title,
                                text: response.message
                            });
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        },
                        error: function(xhr) {
                            if (xhr.status === 422) {
                                var errors = xhr.responseJSON.errors;
                                $.each(errors, function(key, value) {
                                    var input = $('[name="' + key + '"]');
                                    input.addClass('is-invalid');
                                    input.after('<span class="error">' + value[0] + '</span>');
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: 'Update failed. Please try again.'
                                });
                            }
                        }
                    });
                }
            });
            // Reset edit modal validation when opened
            $('#editStaffModal').on('show.bs.modal', function() {
                if ($("#editStaffForm").data('validator')) {
                    $("#editStaffForm").validate().resetForm();
                    $('.is-invalid').removeClass('is-invalid');
                    $('.error').remove();
                }
            });
            // Function to populate availability data in the modal
            function populateAvailabilityData(availabilityData, vacationsData, recurringData) {
                // Store data in hidden inputs
                $('#edit-availability-data').val(JSON.stringify(availabilityData));
                $('#edit-vacations-data').val(JSON.stringify(vacationsData));
                $('#edit-recurring-data').val(JSON.stringify(recurringData));
                // Set global variables for the availability modal to use
                if (typeof window.setEditAvailabilityData === 'function') {
                    window.setEditAvailabilityData(availabilityData, vacationsData, recurringData);
                }
            }
            // Function to clear availability data
            function clearAvailabilityData() {
                $('#edit-availability-data').val('');
                $('#edit-vacations-data').val('');
                $('#edit-recurring-data').val('');
                // Clear global variables
                if (typeof window.clearEditAvailabilityData === 'function') {
                    window.clearEditAvailabilityData();
                }
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/business/staffs/index.blade.php ENDPATH**/ ?>