<div>
    <form
        action="{{ isset($service) ? route('services.update', ['service' => $service->ids, 'type' => 'individual']) : route('services.store', ['type' => 'individual']) }}"
        method="POST" enctype="multipart/form-data" class="form-add-services" id="individualServiceForm">
        @csrf
        @isset($service)
            @method('PUT')
        @endisset

        {{-- Error Summary Container --}}
        <div id="error-summary" class="alert alert-danger" style="display: none;">
            <h5><i class="fas fa-exclamation-triangle"></i> Please fix the following errors:</h5>
            <ul id="error-list"></ul>
        </div>

        <div class="row row-gap-5">
            {{-- Service Name --}}
            <div class="col-md-12">
                <label for="service-name" class="form-label form-input-labels">
                    Service Name
                </label>
                <input type="text" class="form-control form-inputs-field" placeholder="Enter Service name"
                    id="service-name" name="name" value="{{ old('name', $service->name ?? '') }}">
                @error('name')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Name End --}}


            {{-- Category Name --}}
            <div class="col-md-6">
                <label for="category" class="form-label form-input-labels">Category</label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" id="category" name="category_id">
                    <option></option>
                    @forelse ($categories as $category)
                        <option value="{{ $category->ids }}"
                            {{ old('category_id', $service->category->ids ?? '') == $category->ids ? 'selected' : '' }}>
                            {{ $category->name }}</option>
                    @empty
                        <option value="">No categories found</option>
                    @endforelse
                </select>
                @error('category_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Category Name End --}}


            {{-- Subcategory --}}
            <div class="col-md-6">
                <label for="subcategory" class="form-label form-input-labels">Sub
                    Category</label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" data-allow-clear="true"
                    id="subcategory" name="subcategory_id">
                    <option value="">Select Subcategory</option>
                    @if (isset($service) && $service->category)
                        @forelse ($service->category->subcategories as $subcategory)
                            <option value="{{ $subcategory->ids }}"
                                {{ old('subcategory_id', $service->subcategory->ids ?? '') == $subcategory->ids ? 'selected' : '' }}>
                                {{ $subcategory->name }}</option>
                        @empty
                        @endforelse
                    @endif
                </select>
                @error('subcategory_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Subcategory End --}}


            @if (auth()->check() && auth()->user()->hasRole('business'))
                <div class="col-md-12">
                    <label for="staff-member" class="form-label form-input-labels">Assign Staff
                        Member</label>
                    <select class="form-select form-select-field" data-control="select2"
                        data-dropdown-css-class="w-619px" data-close-on-select="false"
                        data-placeholder="Select staff members" data-allow-clear="true" multiple="multiple"
                        id="staff-member" name="staff_ids[]">
                        @forelse ($staffs as $staff)
                            <option value="{{ $staff->id }}"
                                {{ isset($service) && $service->staff->contains('id', $staff->id) ? 'selected' : '' }}>
                                {{ $staff->name }}</option>
                        @empty
                            <option disabled>No staff members available</option>
                        @endforelse
                    </select>
                    @error('staff_ids')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>
            @endif

            {{-- Availability --}}
            <div class="col-md-12">
                <x-service-availability-component :availabilities="$service->availabilities ?? []" />
            </div>
            {{-- Availability End --}}


            {{-- Service Duration --}}
            <div class="col-md-6">
                <label for="duration" class="form-label form-input-labels">Service Duration</label>
                <select name="duration" id="duration" class="form-control form-select-field">
                    <option value="">Select Service Duration</option>
                    <option value="15" {{ old('duration', $service->duration ?? '') == '15' ? 'selected' : '' }}>15
                        min
                    </option>
                    <option value="30" {{ old('duration', $service->duration ?? '') == '30' ? 'selected' : '' }}>30
                        min
                    </option>
                    <option value="45" {{ old('duration', $service->duration ?? '') == '45' ? 'selected' : '' }}>40
                        min
                    </option>
                    <option value="60" {{ old('duration', $service->duration ?? '') == '60' ? 'selected' : '' }}>60
                        min
                    </option>
                </select>
                {{-- <input type="number" class="form-control form-inputs-field"
                    placeholder="Enter Services Duration" id="duration" name="duration"> --}}
                <!-- <select class="form-select form-select-field" id="duration" name="duration"
                    data-control="select2" data-placeholder="Select">
                    <option></option>
                    <option value="30 min">30 min</option>
                    <option value="40 min">40 min</option>
                </select> -->
                @error('duration')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Duration End --}}


            {{-- Price --}}
            <div class="col-md-3">
                <label for="price" class="form-label form-input-labels">
                    Price <span class="normal opacity-6 light-black">(Inclusive VAT)</span>
                </label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter price" id="price"
                    name="price" value="{{ old('price', $service->price ?? '') }}">
                @error('price')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Price End --}}

            {{-- Additional Costs --}}
            <div class="col-md-3">
                <label for="tax" class="form-label form-input-labels">Additional Costs</label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter additional cost"
                    id="tax" name="additional_cost"
                    value="{{ old('additional_cost', $service->additional_cost ?? '') }}">
                @error('additional_cost')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Additional Costs End --}}

            {{-- Required Items --}}
            <div class="col-md-12">
                <label for="required-items" class="form-label form-input-labels">Required
                    Items For Service</label>
                <input type="text" class="form-control form-inputs-field"
                    placeholder="Enter required items (separate with comma)" id="required-items"
                    name="required_items" value="{{ old('required_items', $service->required_items ?? '') }}">
                @error('required_items')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Required Items End --}}

            {{-- Description --}}
            <div class="col-md-12">
                <label for="description" class="form-label form-input-labels">Description</label>
                <textarea class="form-control form-inputs-field form-textarea-field" id="description" name="description"
                    rows="4" placeholder="Enter Description here">{{ old('description', $service->description ?? '') }}</textarea>
                @error('description')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Description End --}}

            {{-- Onsite & Customer Location --}}
            <div class="col-md-4 d-flex gap-4">
                <label class="styled-checkbox d-flex gap-3">
                    <input type="checkbox" name="is_onsite" value="1" id="onsite-secondary"
                        @checked(old('is_onsite', $service->is_onsite ?? false))>
                    <span class="fs-14 light-black normal">On-site</span>
                </label>

                <label class="styled-checkbox d-flex gap-3">
                    <input type="checkbox" name="is_customer_location" value="1" @checked(old('is_customer_location', $service->is_customer_location ?? false))
                        id="customer-location-secondary">
                    <span class="fs-14 light-black normal">Customer Location</span>
                </label>
            </div>
            {{-- Onsite & Customer Location End --}}

            <div class="row row-gap-5">
                {{-- Physical Location --}}
                <div class="col-md-12 form-hide-box" id="physical-location-field">
                    <label for="physical-location-secondary" class="form-label form-input-labels">Physical
                        Location</label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field" id="pac-input"
                        name="physical_location"
                        value="{{ old('physical_location', $service->physical_location ?? '') }}"
                        placeholder="2715 Ash Dr. San Jose, South Dakota 83475">
                    <input type="hidden" name="lat" value="" id="latitude">
                    <input type="hidden" name="lng" value="" id="longitude">
                    <div class="custom_loc mt-2">
                        <div id="map" style="height: 300px"></div>
                    </div>
                    @error('physical_location')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>
                {{-- Physical Location End --}}

                {{-- Radius, Travel Time, Additional Service Charges --}}
                <div class="col-md-4 form-hide-box" id="radius-field">
                    <label for="radius" class="form-label form-input-labels">Radius</label>
                    <input type="number" class="form-control form-inputs-field" placeholder="Enter radius"
                        value="{{ old('radius', $service->radius ?? '') }}" id="radius" name="radius">
                    @error('radius')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <div class="col-md-4 form-hide-box" id="traveltime-field">
                    <label for="traveltime" class="form-label form-input-labels">Travel
                        Time</label>
                    <input type="number" class="form-control form-inputs-field"
                        value="{{ old('travel_time', $service->travel_time ?? '') }}" placeholder="Enter travel time"
                        id="traveltime" name="travel_time">
                    @error('travel_time')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <div class="col-md-4 form-hide-box" id="servicecharges-field">
                    <label for="servicecharges" class="form-label form-input-labels">Additional
                        Service Charges</label>
                    <input type="number" class="form-control form-inputs-field"
                        placeholder="Enter additional service charges" id="servicecharges" name="service_charges"
                        value="{{ old('service_charges', $service->service_charges ?? '') }}">
                    @error('service_charges')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>
            </div>

            {{-- Thumbnail Image --}}
            <div class="col-md-4 ">
                <label for="thumbnail-secondary" class="form-label form-input-labels">Thumbnail
                    Image</label>
                <div class="position-relative ">
                    <div class="image-input {{ $service->image ?? null ? 'image-input-changed' : 'image-input-empty' }}"
                        data-kt-image-input="true">
                        <div class="image-input-wrapper"
                            style="background-image: url('{{ asset('website') . '/' . ($service->image ?? '') }}');">
                        </div>
                        <label
                            class="image-label flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                            data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Change avatar">
                            <i class="bi bi-upload upload-icon"></i>
                            <span>Upload Image</span>
                            <span>50x50 px</span>
                            <input type="file" name="thumbnail" accept=".png, .jpg, .jpeg" />
                        </label>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Cancel avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Remove avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                    </div>
                </div>
                @error('thumbnail')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Thumbnail Image End --}}
            <div class="">
                <button type="submit" class="add-btn">
                    {{ $btn_text ?? 'Add' }}
                </button>
            </div>
        </div>
    </form>
</div>


@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script>
        // Category change handler for subcategories
        $(document).ready(function() {
            $('#category').on('change', function() {
                const categoryId = $(this).val();
                const subcategorySelect = $('#subcategory');

                // Clear subcategory options
                subcategorySelect.empty().append('<option value="">Select Subcategory</option>');

                if (categoryId) {
                    // Show loading state
                    subcategorySelect.append('<option disabled>Loading...</option>');

                    // Fetch subcategories
                    $.ajax({
                        url: `{{ route('services.get-subcategories', ':category_id') }}`.replace(':category_id', categoryId),
                        type: 'GET',
                        success: function(response) {
                            // Clear loading option
                            subcategorySelect.find('option[disabled]').remove();

                            if (response.success && response.subcategories.length > 0) {
                                $.each(response.subcategories, function(index, subcategory) {
                                    subcategorySelect.append(`<option value="${subcategory.ids}">${subcategory.name}</option>`);
                                });
                            } else {
                                subcategorySelect.append('<option disabled>No subcategories found</option>');
                            }

                            // Reinitialize Select2 if it's being used
                            if (subcategorySelect.hasClass('select2-hidden-accessible')) {
                                subcategorySelect.select2('destroy').select2();
                            }
                        },
                        error: function() {
                            subcategorySelect.find('option[disabled]').remove();
                            subcategorySelect.append('<option disabled>Error loading subcategories</option>');
                        }
                    });
                }
            });
        });
    </script>
    <script>
        const weekData = {};
        let currentWeekIndex = 0;
        const baseStartDate = moment().startOf('isoWeek'); // This will set the start date to the current week's Monday

        // 🎯 JSON DATA INITIALIZATION - Pass your array JSON data here
        const initializeDataFromJSON = () => {
            // 📋 YOUR JSON DATA - Replace this array with your actual API response
            const availabilityArray = @js($service->availabilities ?? []);

            // Convert array format to week-based format for the calendar
            availabilityArray.forEach(item => {
                const date = moment(item.date);
                const today = moment().startOf('day');

                // 🚫 SKIP PAST DATES - Don't process dates that are in the past
                if (date.isBefore(today, 'day')) {
                    console.log(`Skipping past date: ${item.date} (${item.day})`);
                    return; // Skip this iteration for past dates
                }

                const weekStart = date.clone().startOf('isoWeek'); // Get Monday of that week
                const weekKey = weekStart.format("YYYY-MM-DD");
                const dayName = item.day;

                // Convert time format from "HH:MM:SS" to "HH:MM"
                const startTime = item.start_time.substring(0, 5); // Remove seconds
                const endTime = item.end_time.substring(0, 5); // Remove seconds

                // Initialize week if it doesn't exist
                if (!weekData[weekKey]) {
                    weekData[weekKey] = {
                        Monday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Tuesday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Wednesday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Thursday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Friday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Saturday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        },
                        Sunday: {
                            enabled: false,
                            start: "10:00",
                            end: "19:00"
                        }
                    };
                }

                // Set the specific day data (only for current and future dates)
                weekData[weekKey][dayName] = {
                    enabled: true,
                    start: startTime,
                    end: endTime,
                    id: item.id, // Store original ID for reference
                    service_id: item.service_id // Store service_id for reference
                };
            });

            console.log("Data loaded from JSON array:", weekData);
            console.log("Original array data:", availabilityArray);
        };

        // 🎯 EXTRACT SELECTED AVAILABILITY IN YOUR DESIRED FORMAT
        const getSelectedAvailability = () => {
            const selectedAvailability = [];

            // Loop through all weeks in weekData
            Object.keys(weekData).forEach(weekKey => {
                const weekStart = moment(weekKey); // Monday of the week
                const weekDays = weekData[weekKey];

                // Check each day of the week
                Object.keys(weekDays).forEach(dayName => {
                    const dayData = weekDays[dayName];

                    // Only include enabled days
                    if (dayData.enabled) {
                        // Calculate the actual date for this day
                        const dayIndex = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday",
                            "Saturday", "Sunday"
                        ].indexOf(dayName);
                        const actualDate = weekStart.clone().add(dayIndex, 'days');

                        // Add to result array in your desired format
                        selectedAvailability.push({
                            "date": actualDate.format("YYYY-MM-DD"),
                            "day": dayName,
                            "start": dayData.start,
                            "end": dayData.end
                        });
                    }
                });
            });

            // Sort by date for better organization
            selectedAvailability.sort((a, b) => moment(a.date).diff(moment(b.date)));

            return selectedAvailability;
        };

        // 📝 UPDATE TEXTAREA WITH JSON OUTPUT
        const updateJsonOutput = () => {
            const selectedData = getSelectedAvailability();
            const jsonString = JSON.stringify(selectedData, null, 2);
            $("#jsonOutput").val(jsonString);
        };

        const updateWeekUI = () => {
            const startOfWeek = baseStartDate.clone().add(currentWeekIndex * 7, "days");
            const weekDays = Array.from({
                length: 7
            }, (_, i) => startOfWeek.clone().add(i, "days"));
            const weekRange = `${weekDays[0].format("DD MMM YYYY")} - ${weekDays[6].format("DD MMM YYYY")}`;
            const weekKey = startOfWeek.format("YYYY-MM-DD");
            const week = weekData[weekKey] || {};

            $("#weekRange").text(weekRange);

            const dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
            const today = moment().startOf('day');

            const html = dayNames.map((day, index) => {
                const date = weekDays[index];
                const formattedDate = date.format("YYYY-MM-DD");
                const val = week[day] || {
                    start: "10:00",
                    end: "19:00",
                    enabled: false
                };

                // 🚫 CHECK IF DATE IS IN THE PAST
                const isPastDate = date.isBefore(today, 'day');

                return `
                    <div class="d-flex align-items-center mb-2 day-row" data-day="${day}" data-date="${formattedDate}">
                        <input type="checkbox" class="form-check-input me-2 day-checkbox" data-day="${day}"
                               ${val.enabled ? "checked" : ""} ${isPastDate ? "disabled" : ""}>
                        <label class="me-2 ${isPastDate ? 'text-muted' : ''}" style="width: 100px;">${day}</label>
                        ${val.enabled && !isPastDate ? `
                                            <input type="time" class="form-control form-control-sm me-2 start-time" value="${val.start}" data-day="${day}" style="width:120px;">
                                            <span class="me-2">to</span>
                                            <input type="time" class="form-control form-control-sm end-time" value="${val.end}" data-day="${day}" style="width:120px;">
                                        ` : `<span class="text-muted">${isPastDate ? "Past Date" : "Closed"}</span>`}
                    </div>`;
            }).join("");

            $("#weekDaysContainer").html(html);
        };

        const saveCurrentWeekData = () => {
            const base = baseStartDate.clone().add(currentWeekIndex * 7, "days");
            const key = base.format("YYYY-MM-DD");
            weekData[key] = {};

            $(".day-row").each(function() {
                const day = $(this).data("day");
                const enabled = $(this).find(".day-checkbox").is(":checked");
                const start = $(this).find(".start-time").val() || "10:00";
                const end = $(this).find(".end-time").val() || "19:00";
                weekData[key][day] = {
                    enabled,
                    start,
                    end
                };
            });
        };

        // Function to duplicate the weeks (with reset functionality)
        const duplicateWeeks = (weeks) => {
            const current = baseStartDate.clone().add(currentWeekIndex * 7, "days");
            const srcKey = current.format("YYYY-MM-DD");

            // 🔄 RESET: Clear all future week duplications first
            Object.keys(weekData).forEach(weekKey => {
                const weekDate = moment(weekKey);
                const currentWeekDate = moment(srcKey);

                // Remove any week that's after the current week and was previously duplicated
                if (weekDate.isAfter(currentWeekDate, 'week')) {
                    // Check if this week has the same pattern as current week (indicating it was duplicated)
                    const currentWeekData = weekData[srcKey];
                    const weekToCheck = weekData[weekKey];

                    // Only remove if it looks like a duplication (same enabled pattern)
                    if (currentWeekData && weekToCheck) {
                        const currentEnabledDays = Object.keys(currentWeekData).filter(day => currentWeekData[
                            day].enabled);
                        const checkEnabledDays = Object.keys(weekToCheck).filter(day => weekToCheck[day]
                            .enabled);

                        // If same number of enabled days, likely a duplication - remove it
                        if (currentEnabledDays.length === checkEnabledDays.length && currentEnabledDays.length >
                            0) {
                            delete weekData[weekKey];
                        }
                    }
                }
            });

            // 📅 CREATE: Now create fresh duplications for the selected weeks
            for (let i = 1; i < weeks; i++) {
                const next = current.clone().add(i * 7, "days");
                const newKey = next.format("YYYY-MM-DD");

                // Create deep copy of current week's data
                weekData[newKey] = JSON.parse(JSON.stringify(weekData[srcKey]));
            }

            console.log(`Reset and duplicated for ${weeks} weeks from ${srcKey}`);
        };

        // Validate time inputs
        const validateTimeInput = (input) => {
            const $input = $(input);
            const startTime = $input.val();
            const endTimeInput = $input.hasClass('start-time') ?
                $input.closest('.day-row').find('.end-time') :
                $input.closest('.day-row').find('.start-time');
            const endTime = endTimeInput.val();

            // Basic validation to ensure start time is before end time
            if (startTime && endTime && startTime >= endTime) {
                alert('Start time must be before end time');
                $input.focus();
            }
        };

        $(document).ready(function() {
            // Initialize data from JSON - THIS IS WHERE YOU PASS YOUR JSON DATA
            initializeDataFromJSON();

            updateWeekUI();
            updateJsonOutput(); // Initialize JSON output

            $(document).on("change", ".day-checkbox", function() {
                saveCurrentWeekData();
                updateWeekUI();
                updateJsonOutput(); // Update JSON when checkbox changes
            });

            // Validate time inputs when they change
            $(document).on("change", ".start-time, .end-time", function() {
                validateTimeInput(this);
                saveCurrentWeekData();
                updateJsonOutput(); // Update JSON when time changes
            });

            $("#prevWeek").click(function() {
                saveCurrentWeekData();
                currentWeekIndex--;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            });

            $("#nextWeek").click(function() {
                saveCurrentWeekData();
                currentWeekIndex++;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            });

            $("#saveAvailability").click(function() {
                saveCurrentWeekData();
                updateJsonOutput(); // Final update of JSON

                const selectedData = getSelectedAvailability();
                alert("Availability Saved!");
                console.log("📅 Selected Availability Array:", selectedData);
                console.log("📋 Full week data structure:", weekData);
            });

            // Recurring Radio Button Change
            $("input[name='recurring']").change(function() {
                const selected = $(this).val();
                saveCurrentWeekData();

                if (selected === "custom") {
                    $(".custom-weeks-input").show();
                } else {
                    $(".custom-weeks-input").hide();
                }

                if (selected === "custom") {
                    return false;
                }

                const repeatWeeks = parseInt(selected);
                console.log("repeatWeeks here", repeatWeeks);

                if (repeatWeeks > 0) {
                    duplicateWeeks(repeatWeeks);
                    updateJsonOutput(); // Update JSON after duplication
                    alert(`Availability reset and duplicated for ${repeatWeeks} weeks total.`);
                }
            });

            // Handle Custom Weeks Input
            $("#customDone").click(function() {
                const customWeeks = parseInt($("#customWeeks").val());
                if (!isNaN(customWeeks) && customWeeks > 0) {
                    saveCurrentWeekData();
                    duplicateWeeks(customWeeks);
                    updateJsonOutput(); // Update JSON after custom duplication
                    alert(`Availability reset and duplicated for ${customWeeks} weeks total.`);
                    $(".custom-weeks-input").hide();
                    $("input[name='recurring']").prop('checked', false); // Uncheck radio buttons
                } else {
                    alert("Please enter a valid number of weeks.");
                }
            });
        });
    </script>
@endpush
