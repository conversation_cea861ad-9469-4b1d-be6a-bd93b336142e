<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\HasUuid;

class Staff extends Model
{
    use HasFactory,HasUuid;
    protected $table = 'staffs';
    protected $fillable = [
        'ids',
        'user_id',
        'image',
        'name',
        'email',
        'category_id',
        'subcategory_id',
        'phone',
        'facebook',
        'instagram',
        'tiktok',
        'youtube',
        'status',
        'is_recurring',
        'recurring_duration',
        'custom_weeks',
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }
    public function subcategory()
    {
        return $this->belongsTo(SubCategory::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function availabilities()
    {
        return $this->hasMany(StaffAvailability::class, 'staff_id');
    }

    public function vacations()
    {
        return $this->hasMany(StaffVacation::class, 'staff_id');
    }
}
